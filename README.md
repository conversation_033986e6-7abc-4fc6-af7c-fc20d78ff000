# Docker Management Tool

A comprehensive Docker management platform with FastAPI backend and React frontend for managing Docker containers, networks, and resources.

## Features

- **Docker Network Management**: Create and manage macvlan networks with IP allocation
- **Container Lifecycle**: Automated container creation, management, and cleanup
- **Scheduling System**: Cron-like scheduling for container start/stop operations
- **Real-time Monitoring**: Live dashboard with container stats, network usage, and CPU monitoring
- **Traffic Control**: Bandwidth limiting and traffic shaping integration

## Architecture

### Backend (FastAPI)
- RESTful API with OpenAPI/Swagger documentation
- Docker API integration for container and network management
- WebSocket support for real-time monitoring
- Scheduling system with custom script execution
- Traffic control command generation

### Frontend (React + Material-UI)
- Responsive web interface
- Real-time dashboard with live updates
- Network and container management forms
- Monitoring charts and statistics

## Project Structure

```
docker_map/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core functionality
│   │   ├── models/         # Data models
│   │   ├── services/       # Business logic
│   │   └── main.py         # FastAPI application
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── App.js
│   ├── package.json
│   └── Dockerfile
├── docker-compose.yml      # Development environment
└── README.md
```

## Quick Start

### Development Setup

1. **Backend Setup**:
   ```bash
   cd backend
   pip install -r requirements.txt
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Frontend Setup**:
   ```bash
   cd frontend
   npm install
   npm start
   ```

3. **Docker Compose** (Recommended):
   ```bash
   docker-compose up -d
   ```

### API Documentation

Once the backend is running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### Frontend Application

Access the web interface at: http://localhost:3000

## Requirements

- Docker and Docker Compose
- Python 3.8+
- Node.js 16+
- Root/sudo access for network management operations

## License

MIT License

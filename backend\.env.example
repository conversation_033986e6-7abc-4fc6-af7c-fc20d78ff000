# Docker Management Tool - Environment Configuration

# API Configuration
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Docker Configuration
DOCKER_HOST=unix:///var/run/docker.sock
DOCKER_API_VERSION=auto

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Monitoring Configuration
MONITORING_INTERVAL=5
MAX_WEBSOCKET_CONNECTIONS=100

# Scheduling Configuration
SCHEDULER_TIMEZONE=UTC
MAX_CONCURRENT_JOBS=10

# Traffic Control Configuration
TC_BINARY_PATH=/sbin/tc
IP_BINARY_PATH=/sbin/ip
IPTABLES_BINARY_PATH=/usr/sbin/iptables

# Logging Configuration
LOG_LEVEL=INFO

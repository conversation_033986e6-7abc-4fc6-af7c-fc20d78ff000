from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from app.services.docker_service import DockerService
from app.models.container_models import (
    ContainerCreate,
    ContainerResponse,
    ContainerUpdate,
    ContainerStats
)

router = APIRouter()

def get_docker_service() -> DockerService:
    return DockerService()

class VolumeMount(BaseModel):
    host_path: str
    container_path: str
    mode: str = "rw"

class ContainerCreateRequest(BaseModel):
    name: str
    image: str
    command: Optional[str] = None
    environment: Optional[Dict[str, str]] = None
    ports: Optional[Dict[str, int]] = None
    volumes: Optional[List[VolumeMount]] = None
    network: Optional[str] = None
    ip_address: Optional[str] = None
    restart_policy: str = "unless-stopped"
    detach: bool = True

@router.get("/", response_model=List[ContainerResponse])
async def list_containers(
    all: bool = True,
    docker_service: DockerService = Depends(get_docker_service)
):
    """List all containers"""
    try:
        containers = await docker_service.list_containers(all=all)
        return containers
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{container_id}", response_model=ContainerResponse)
async def get_container(
    container_id: str,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Get container details by ID or name"""
    try:
        container = await docker_service.get_container(container_id)
        if not container:
            raise HTTPException(status_code=404, detail="Container not found")
        return container
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=ContainerResponse)
async def create_container(
    container_data: ContainerCreateRequest,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Create a new container"""
    try:
        # Convert volumes to the expected format
        volumes = {}
        if container_data.volumes:
            for vol in container_data.volumes:
                volumes[vol.host_path] = {
                    'bind': vol.container_path,
                    'mode': vol.mode
                }
        
        container = await docker_service.create_container(
            name=container_data.name,
            image=container_data.image,
            command=container_data.command,
            environment=container_data.environment,
            ports=container_data.ports,
            volumes=volumes,
            network=container_data.network,
            ip_address=container_data.ip_address,
            restart_policy=container_data.restart_policy,
            detach=container_data.detach
        )
        return container
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{container_id}/start")
async def start_container(
    container_id: str,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Start a container"""
    try:
        result = await docker_service.start_container(container_id)
        return {"message": f"Container {container_id} started successfully", "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{container_id}/stop")
async def stop_container(
    container_id: str,
    timeout: int = 10,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Stop a container"""
    try:
        result = await docker_service.stop_container(container_id, timeout=timeout)
        return {"message": f"Container {container_id} stopped successfully", "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{container_id}/restart")
async def restart_container(
    container_id: str,
    timeout: int = 10,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Restart a container"""
    try:
        result = await docker_service.restart_container(container_id, timeout=timeout)
        return {"message": f"Container {container_id} restarted successfully", "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{container_id}")
async def delete_container(
    container_id: str,
    force: bool = False,
    remove_volumes: bool = False,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Delete a container"""
    try:
        result = await docker_service.remove_container(
            container_id, 
            force=force, 
            remove_volumes=remove_volumes
        )
        return {"message": f"Container {container_id} deleted successfully", "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{container_id}/stats", response_model=ContainerStats)
async def get_container_stats(
    container_id: str,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Get real-time container statistics"""
    try:
        stats = await docker_service.get_container_stats(container_id)
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{container_id}/logs")
async def get_container_logs(
    container_id: str,
    tail: int = 100,
    follow: bool = False,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Get container logs"""
    try:
        logs = await docker_service.get_container_logs(
            container_id, 
            tail=tail, 
            follow=follow
        )
        return {"logs": logs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

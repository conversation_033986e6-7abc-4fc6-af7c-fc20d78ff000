from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import platform
import socket
import docker
import psutil
from datetime import datetime, timezone

from app.core.config import settings

router = APIRouter()

@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Health check endpoint for server status monitoring
    """
    try:
        # Basic system information
        hostname = socket.gethostname()
        
        # Docker connection check
        docker_status = "unknown"
        docker_version = "unknown"
        try:
            client = docker.DockerClient(
                base_url=settings.DOCKER_HOST,
                version=settings.DOCKER_API_VERSION
            )
            client.ping()
            docker_info = client.version()
            docker_status = "connected"
            docker_version = docker_info.get("Version", "unknown")
            client.close()
        except Exception as e:
            docker_status = f"error: {str(e)}"
        
        # System resources
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Boot time and uptime
        boot_time = datetime.fromtimestamp(psutil.boot_time(), tz=timezone.utc)
        uptime = (datetime.now(timezone.utc) - boot_time).total_seconds()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "version": "1.0.0",  # You can make this dynamic
            "hostname": hostname,
            "platform": {
                "system": platform.system(),
                "release": platform.release(),
                "machine": platform.machine(),
                "processor": platform.processor(),
            },
            "docker": {
                "status": docker_status,
                "version": docker_version,
                "host": settings.DOCKER_HOST,
            },
            "resources": {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                },
                "disk": {
                    "total": disk.total,
                    "free": disk.free,
                    "percent": (disk.used / disk.total * 100) if disk.total > 0 else 0,
                },
                "uptime_seconds": uptime,
            },
            "services": {
                "api": "running",
                "monitoring": "running",
                "scheduling": "running",
                "traffic_control": "running",
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@router.get("/")
async def root() -> Dict[str, Any]:
    """
    Root endpoint with basic server information
    """
    try:
        hostname = socket.gethostname()
        
        return {
            "name": "Docker Management Tool API",
            "version": "1.0.0",
            "hostname": hostname,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "docs_url": "/docs",
            "health_url": "/health",
            "api_prefix": "/api/v1",
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Server info failed: {str(e)}")

@router.get("/ping")
async def ping() -> Dict[str, str]:
    """
    Simple ping endpoint for basic connectivity testing
    """
    return {
        "status": "pong",
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }

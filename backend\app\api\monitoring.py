from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.services.monitoring_service import MonitoringService
from app.models.monitoring_models import (
    SystemStats,
    ContainerStats,
    NetworkStats,
    MonitoringResponse
)

router = APIRouter()

def get_monitoring_service() -> MonitoringService:
    return MonitoringService()

@router.get("/system", response_model=SystemStats)
async def get_system_stats(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """Get current system statistics"""
    try:
        stats = await monitoring_service.get_system_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/containers", response_model=List[ContainerStats])
async def get_all_container_stats(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """Get statistics for all running containers"""
    try:
        stats = await monitoring_service.get_all_container_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/containers/{container_id}", response_model=ContainerStats)
async def get_container_stats(
    container_id: str,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """Get statistics for a specific container"""
    try:
        stats = await monitoring_service.get_container_stats(container_id)
        if not stats:
            raise HTTPException(status_code=404, detail="Container not found or not running")
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/networks", response_model=List[NetworkStats])
async def get_network_stats(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """Get network interface statistics"""
    try:
        stats = await monitoring_service.get_network_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/networks/{interface_name}", response_model=NetworkStats)
async def get_interface_stats(
    interface_name: str,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """Get statistics for a specific network interface"""
    try:
        stats = await monitoring_service.get_interface_stats(interface_name)
        if not stats:
            raise HTTPException(status_code=404, detail="Network interface not found")
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dashboard", response_model=MonitoringResponse)
async def get_dashboard_data(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """Get comprehensive monitoring data for dashboard"""
    try:
        # Get all monitoring data
        system_stats = await monitoring_service.get_system_stats()
        container_stats = await monitoring_service.get_all_container_stats()
        network_stats = await monitoring_service.get_network_stats()
        
        dashboard_data = MonitoringResponse(
            timestamp=datetime.utcnow(),
            system=system_stats,
            containers=container_stats,
            networks=network_stats
        )
        
        return dashboard_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/containers/{container_id}/bandwidth")
async def get_container_bandwidth(
    container_id: str,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """Get real-time bandwidth usage for a container"""
    try:
        bandwidth = await monitoring_service.get_container_bandwidth(container_id)
        return {
            "container_id": container_id,
            "bandwidth": bandwidth,
            "timestamp": datetime.utcnow()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/containers/{container_id}/history")
async def get_container_stats_history(
    container_id: str,
    hours: int = 24,
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """Get historical statistics for a container"""
    try:
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours)
        
        history = await monitoring_service.get_container_stats_history(
            container_id, 
            start_time, 
            end_time
        )
        
        return {
            "container_id": container_id,
            "start_time": start_time,
            "end_time": end_time,
            "history": history
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/alerts")
async def get_monitoring_alerts(
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """Get current monitoring alerts and warnings"""
    try:
        alerts = await monitoring_service.get_alerts()
        return {"alerts": alerts, "timestamp": datetime.utcnow()}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/alerts/thresholds")
async def set_alert_thresholds(
    thresholds: Dict[str, float],
    monitoring_service: MonitoringService = Depends(get_monitoring_service)
):
    """Set monitoring alert thresholds"""
    try:
        result = await monitoring_service.set_alert_thresholds(thresholds)
        return {
            "message": "Alert thresholds updated successfully",
            "thresholds": thresholds,
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def monitoring_health_check():
    """Health check for monitoring service"""
    return {
        "status": "healthy",
        "service": "monitoring",
        "timestamp": datetime.utcnow()
    }

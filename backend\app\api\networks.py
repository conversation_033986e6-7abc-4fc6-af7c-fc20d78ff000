from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import ipaddress

from app.services.docker_service import DockerService
from app.models.network_models import (
    NetworkCreate,
    NetworkResponse,
    NetworkUpdate,
    IPAllocation
)

router = APIRouter()

def get_docker_service() -> DockerService:
    return DockerService()

class MacvlanNetworkCreate(BaseModel):
    name: str
    subnet: str  # CIDR notation, e.g., "***********/24"
    gateway: str  # Gateway IP, e.g., "***********"
    parent_interface: str  # Host network interface, e.g., "eth0"
    ip_range: Optional[str] = None  # Optional IP range for allocation
    options: Optional[Dict[str, str]] = None

class IPAllocationRequest(BaseModel):
    container_id: str
    ip_address: str

@router.get("/", response_model=List[NetworkResponse])
async def list_networks(
    docker_service: DockerService = Depends(get_docker_service)
):
    """List all Docker networks"""
    try:
        networks = await docker_service.list_networks()
        return networks
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{network_id}", response_model=NetworkResponse)
async def get_network(
    network_id: str,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Get network details by ID or name"""
    try:
        network = await docker_service.get_network(network_id)
        if not network:
            raise HTTPException(status_code=404, detail="Network not found")
        return network
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/macvlan", response_model=NetworkResponse)
async def create_macvlan_network(
    network_data: MacvlanNetworkCreate,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Create a macvlan Docker network"""
    try:
        # Validate subnet and gateway
        try:
            subnet_network = ipaddress.IPv4Network(network_data.subnet, strict=False)
            gateway_ip = ipaddress.IPv4Address(network_data.gateway)
            
            # Ensure gateway is within subnet
            if gateway_ip not in subnet_network:
                raise HTTPException(
                    status_code=400, 
                    detail="Gateway IP must be within the specified subnet"
                )
        except ipaddress.AddressValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid IP address format: {e}")
        
        # Create macvlan network configuration
        ipam_config = {
            "Config": [{
                "Subnet": network_data.subnet,
                "Gateway": network_data.gateway,
            }]
        }
        
        if network_data.ip_range:
            ipam_config["Config"][0]["IPRange"] = network_data.ip_range
        
        options = network_data.options or {}
        options["parent"] = network_data.parent_interface
        
        network = await docker_service.create_network(
            name=network_data.name,
            driver="macvlan",
            options=options,
            ipam=ipam_config
        )
        return network
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=NetworkResponse)
async def create_network(
    network_data: NetworkCreate,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Create a Docker network"""
    try:
        network = await docker_service.create_network(
            name=network_data.name,
            driver=network_data.driver,
            options=network_data.options,
            ipam=network_data.ipam
        )
        return network
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{network_id}")
async def delete_network(
    network_id: str,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Delete a Docker network"""
    try:
        result = await docker_service.remove_network(network_id)
        return {"message": f"Network {network_id} deleted successfully", "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{network_id}/connect")
async def connect_container_to_network(
    network_id: str,
    allocation: IPAllocationRequest,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Connect a container to a network with specific IP"""
    try:
        # Validate IP address format
        try:
            ipaddress.IPv4Address(allocation.ip_address)
        except ipaddress.AddressValueError:
            raise HTTPException(status_code=400, detail="Invalid IP address format")
        
        result = await docker_service.connect_container_to_network(
            network_id=network_id,
            container_id=allocation.container_id,
            ip_address=allocation.ip_address
        )
        return {
            "message": f"Container {allocation.container_id} connected to network {network_id}",
            "ip_address": allocation.ip_address,
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{network_id}/disconnect")
async def disconnect_container_from_network(
    network_id: str,
    container_id: str,
    force: bool = False,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Disconnect a container from a network"""
    try:
        result = await docker_service.disconnect_container_from_network(
            network_id=network_id,
            container_id=container_id,
            force=force
        )
        return {
            "message": f"Container {container_id} disconnected from network {network_id}",
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{network_id}/containers")
async def get_network_containers(
    network_id: str,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Get all containers connected to a network"""
    try:
        containers = await docker_service.get_network_containers(network_id)
        return {"network_id": network_id, "containers": containers}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{network_id}/available-ips")
async def get_available_ips(
    network_id: str,
    limit: int = 50,
    docker_service: DockerService = Depends(get_docker_service)
):
    """Get available IP addresses in a network"""
    try:
        available_ips = await docker_service.get_available_network_ips(network_id, limit=limit)
        return {"network_id": network_id, "available_ips": available_ips}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

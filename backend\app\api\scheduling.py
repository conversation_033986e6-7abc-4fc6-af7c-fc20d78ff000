from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel

from app.services.scheduling_service import SchedulingService
from app.models.scheduling_models import (
    ScheduleCreate,
    ScheduleResponse,
    ScheduleUpdate,
    JobExecution
)

router = APIRouter()

def get_scheduling_service() -> SchedulingService:
    return SchedulingService()

class ContainerScheduleCreate(BaseModel):
    name: str
    container_id: str
    action: str  # "start", "stop", "restart"
    cron_expression: str  # e.g., "0 9 * * 1-5" for weekdays at 9 AM
    timezone: str = "UTC"
    enabled: bool = True
    script_path: Optional[str] = None  # Path to script to execute
    script_args: Optional[List[str]] = None
    description: Optional[str] = None

class ScriptScheduleCreate(BaseModel):
    name: str
    script_path: str
    script_args: Optional[List[str]] = None
    cron_expression: str
    timezone: str = "UTC"
    enabled: bool = True
    working_directory: Optional[str] = None
    environment: Optional[Dict[str, str]] = None
    description: Optional[str] = None

@router.get("/", response_model=List[ScheduleResponse])
async def list_schedules(
    enabled_only: bool = False,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """List all scheduled jobs"""
    try:
        schedules = await scheduling_service.list_schedules(enabled_only=enabled_only)
        return schedules
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{schedule_id}", response_model=ScheduleResponse)
async def get_schedule(
    schedule_id: str,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Get schedule details by ID"""
    try:
        schedule = await scheduling_service.get_schedule(schedule_id)
        if not schedule:
            raise HTTPException(status_code=404, detail="Schedule not found")
        return schedule
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/container", response_model=ScheduleResponse)
async def create_container_schedule(
    schedule_data: ContainerScheduleCreate,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Create a new container action schedule"""
    try:
        # Validate action
        valid_actions = ["start", "stop", "restart"]
        if schedule_data.action not in valid_actions:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid action. Must be one of: {valid_actions}"
            )
        
        schedule = await scheduling_service.create_container_schedule(
            name=schedule_data.name,
            container_id=schedule_data.container_id,
            action=schedule_data.action,
            cron_expression=schedule_data.cron_expression,
            timezone=schedule_data.timezone,
            enabled=schedule_data.enabled,
            script_path=schedule_data.script_path,
            script_args=schedule_data.script_args,
            description=schedule_data.description
        )
        return schedule
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/script", response_model=ScheduleResponse)
async def create_script_schedule(
    schedule_data: ScriptScheduleCreate,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Create a new script execution schedule"""
    try:
        schedule = await scheduling_service.create_script_schedule(
            name=schedule_data.name,
            script_path=schedule_data.script_path,
            script_args=schedule_data.script_args,
            cron_expression=schedule_data.cron_expression,
            timezone=schedule_data.timezone,
            enabled=schedule_data.enabled,
            working_directory=schedule_data.working_directory,
            environment=schedule_data.environment,
            description=schedule_data.description
        )
        return schedule
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{schedule_id}", response_model=ScheduleResponse)
async def update_schedule(
    schedule_id: str,
    schedule_data: ScheduleUpdate,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Update an existing schedule"""
    try:
        schedule = await scheduling_service.update_schedule(schedule_id, schedule_data)
        if not schedule:
            raise HTTPException(status_code=404, detail="Schedule not found")
        return schedule
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{schedule_id}")
async def delete_schedule(
    schedule_id: str,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Delete a schedule"""
    try:
        result = await scheduling_service.delete_schedule(schedule_id)
        if not result:
            raise HTTPException(status_code=404, detail="Schedule not found")
        return {"message": f"Schedule {schedule_id} deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{schedule_id}/enable")
async def enable_schedule(
    schedule_id: str,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Enable a schedule"""
    try:
        result = await scheduling_service.enable_schedule(schedule_id)
        if not result:
            raise HTTPException(status_code=404, detail="Schedule not found")
        return {"message": f"Schedule {schedule_id} enabled successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{schedule_id}/disable")
async def disable_schedule(
    schedule_id: str,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Disable a schedule"""
    try:
        result = await scheduling_service.disable_schedule(schedule_id)
        if not result:
            raise HTTPException(status_code=404, detail="Schedule not found")
        return {"message": f"Schedule {schedule_id} disabled successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{schedule_id}/execute")
async def execute_schedule_now(
    schedule_id: str,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Execute a schedule immediately"""
    try:
        result = await scheduling_service.execute_schedule_now(schedule_id)
        return {
            "message": f"Schedule {schedule_id} executed successfully",
            "execution_id": result.get("execution_id"),
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{schedule_id}/executions", response_model=List[JobExecution])
async def get_schedule_executions(
    schedule_id: str,
    limit: int = 50,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Get execution history for a schedule"""
    try:
        executions = await scheduling_service.get_schedule_executions(schedule_id, limit=limit)
        return executions
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/executions/recent", response_model=List[JobExecution])
async def get_recent_executions(
    limit: int = 100,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Get recent job executions across all schedules"""
    try:
        executions = await scheduling_service.get_recent_executions(limit=limit)
        return executions
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/validate/cron/{cron_expression}")
async def validate_cron_expression(
    cron_expression: str,
    scheduling_service: SchedulingService = Depends(get_scheduling_service)
):
    """Validate a cron expression and show next execution times"""
    try:
        validation = await scheduling_service.validate_cron_expression(cron_expression)
        return validation
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

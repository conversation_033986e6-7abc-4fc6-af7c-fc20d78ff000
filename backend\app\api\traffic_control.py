from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from app.services.traffic_control_service import TrafficControlService
from app.models.traffic_control_models import (
    TrafficRule,
    TrafficRuleCreate,
    TrafficRuleResponse,
    BandwidthLimit
)

router = APIRouter()

def get_traffic_control_service() -> TrafficControlService:
    return TrafficControlService()

class BandwidthLimitCreate(BaseModel):
    container_id: str
    interface: Optional[str] = None  # Auto-detect if not provided
    ingress_rate: Optional[str] = None  # e.g., "100mbit", "1gbit"
    egress_rate: Optional[str] = None   # e.g., "50mbit", "500mbit"
    burst: Optional[str] = None         # e.g., "10mb"
    description: Optional[str] = None

class TrafficShapingRule(BaseModel):
    container_id: str
    interface: Optional[str] = None
    rule_type: str  # "limit", "delay", "loss", "duplicate"
    parameters: Dict[str, Any]  # Rule-specific parameters
    priority: int = 1
    description: Optional[str] = None

@router.get("/rules", response_model=List[TrafficRuleResponse])
async def list_traffic_rules(
    container_id: Optional[str] = None,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """List all traffic control rules"""
    try:
        rules = await traffic_service.list_rules(container_id=container_id)
        return rules
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/rules/{rule_id}", response_model=TrafficRuleResponse)
async def get_traffic_rule(
    rule_id: str,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Get traffic rule details by ID"""
    try:
        rule = await traffic_service.get_rule(rule_id)
        if not rule:
            raise HTTPException(status_code=404, detail="Traffic rule not found")
        return rule
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/bandwidth-limit", response_model=TrafficRuleResponse)
async def create_bandwidth_limit(
    limit_data: BandwidthLimitCreate,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Create bandwidth limiting rule for a container"""
    try:
        rule = await traffic_service.create_bandwidth_limit(
            container_id=limit_data.container_id,
            interface=limit_data.interface,
            ingress_rate=limit_data.ingress_rate,
            egress_rate=limit_data.egress_rate,
            burst=limit_data.burst,
            description=limit_data.description
        )
        return rule
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/shaping-rule", response_model=TrafficRuleResponse)
async def create_traffic_shaping_rule(
    rule_data: TrafficShapingRule,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Create a traffic shaping rule"""
    try:
        valid_rule_types = ["limit", "delay", "loss", "duplicate", "corrupt"]
        if rule_data.rule_type not in valid_rule_types:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid rule type. Must be one of: {valid_rule_types}"
            )
        
        rule = await traffic_service.create_shaping_rule(
            container_id=rule_data.container_id,
            interface=rule_data.interface,
            rule_type=rule_data.rule_type,
            parameters=rule_data.parameters,
            priority=rule_data.priority,
            description=rule_data.description
        )
        return rule
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/rules/{rule_id}")
async def delete_traffic_rule(
    rule_id: str,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Delete a traffic control rule"""
    try:
        result = await traffic_service.delete_rule(rule_id)
        if not result:
            raise HTTPException(status_code=404, detail="Traffic rule not found")
        return {"message": f"Traffic rule {rule_id} deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/rules/{rule_id}/apply")
async def apply_traffic_rule(
    rule_id: str,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Apply a traffic control rule"""
    try:
        result = await traffic_service.apply_rule(rule_id)
        return {
            "message": f"Traffic rule {rule_id} applied successfully",
            "commands": result.get("commands", []),
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/rules/{rule_id}/remove")
async def remove_traffic_rule(
    rule_id: str,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Remove a traffic control rule from the system"""
    try:
        result = await traffic_service.remove_rule(rule_id)
        return {
            "message": f"Traffic rule {rule_id} removed successfully",
            "commands": result.get("commands", []),
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/container/{container_id}/interface")
async def get_container_interface(
    container_id: str,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Get network interface information for a container"""
    try:
        interface_info = await traffic_service.get_container_interface(container_id)
        return {
            "container_id": container_id,
            "interface_info": interface_info
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/container/{container_id}/current-rules")
async def get_container_current_rules(
    container_id: str,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Get currently applied traffic control rules for a container"""
    try:
        rules = await traffic_service.get_container_current_rules(container_id)
        return {
            "container_id": container_id,
            "active_rules": rules
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/container/{container_id}/clear-all")
async def clear_all_container_rules(
    container_id: str,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Clear all traffic control rules for a container"""
    try:
        result = await traffic_service.clear_container_rules(container_id)
        return {
            "message": f"All traffic rules cleared for container {container_id}",
            "commands": result.get("commands", []),
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/commands/preview/{rule_id}")
async def preview_tc_commands(
    rule_id: str,
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Preview the tc commands that would be executed for a rule"""
    try:
        commands = await traffic_service.preview_rule_commands(rule_id)
        return {
            "rule_id": rule_id,
            "commands": commands
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
async def get_traffic_control_status(
    traffic_service: TrafficControlService = Depends(get_traffic_control_service)
):
    """Get overall traffic control system status"""
    try:
        status = await traffic_service.get_system_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

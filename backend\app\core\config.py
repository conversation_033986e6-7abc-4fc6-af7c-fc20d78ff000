from pydantic import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    """Application settings"""
    
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Docker Management Tool"
    VERSION: str = "1.0.0"
    
    # CORS Configuration
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://frontend:3000"
    ]
    
    # Docker Configuration
    DOCKER_HOST: str = "unix:///var/run/docker.sock"
    DOCKER_API_VERSION: str = "auto"
    
    # Redis Configuration
    REDIS_HOST: str = "redis"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_URL: str = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
    
    # Monitoring Configuration
    MONITORING_INTERVAL: int = 5  # seconds
    MAX_WEBSOCKET_CONNECTIONS: int = 100
    
    # Scheduling Configuration
    SCHEDULER_TIMEZONE: str = "UTC"
    MAX_CONCURRENT_JOBS: int = 10
    
    # Traffic Control Configuration
    TC_BINARY_PATH: str = "/sbin/tc"
    IP_BINARY_PATH: str = "/sbin/ip"
    IPTABLES_BINARY_PATH: str = "/usr/sbin/iptables"
    
    # Security Configuration
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Global settings instance
settings = Settings()

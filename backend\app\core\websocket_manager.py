import asyncio
import json
import logging
from typing import List, Dict, Any
from fastapi import WebSocket
from app.services.docker_service import DockerService
from app.services.monitoring_service import MonitoringService

logger = logging.getLogger(__name__)

class WebSocketManager:
    """Manages WebSocket connections and real-time monitoring"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.monitoring_task: asyncio.Task = None
        self.is_monitoring = False
        self.docker_service = DockerService()
        self.monitoring_service = MonitoringService()
    
    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific WebSocket"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending message to WebSocket: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast a message to all connected WebSockets"""
        if not self.active_connections:
            return
        
        message_str = json.dumps(message)
        disconnected = []
        
        for connection in self.active_connections:
            try:
                await connection.send_text(message_str)
            except Exception as e:
                logger.error(f"Error broadcasting to WebSocket: {e}")
                disconnected.append(connection)
        
        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection)
    
    async def start_monitoring(self):
        """Start the monitoring background task"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Started monitoring background task")
    
    async def stop_monitoring(self):
        """Stop the monitoring background task"""
        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped monitoring background task")
    
    async def _monitoring_loop(self):
        """Main monitoring loop that broadcasts real-time data"""
        while self.is_monitoring:
            try:
                if self.active_connections:
                    # Get container statistics
                    containers_stats = await self.monitoring_service.get_all_container_stats()
                    
                    # Get network statistics
                    network_stats = await self.monitoring_service.get_network_stats()
                    
                    # Get system statistics
                    system_stats = await self.monitoring_service.get_system_stats()
                    
                    # Broadcast monitoring data
                    monitoring_data = {
                        "type": "monitoring_update",
                        "timestamp": asyncio.get_event_loop().time(),
                        "data": {
                            "containers": containers_stats,
                            "networks": network_stats,
                            "system": system_stats
                        }
                    }
                    
                    await self.broadcast(monitoring_data)
                
                # Wait for next monitoring interval
                await asyncio.sleep(5)  # 5 seconds interval
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5)  # Wait before retrying

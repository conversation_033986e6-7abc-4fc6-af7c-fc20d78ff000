from fastapi import Fast<PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import asyncio
import json
from typing import List

from app.api import containers, networks, monitoring, scheduling, traffic_control, health
from app.core.config import settings
from app.core.websocket_manager import WebSocketManager

# Create FastAPI application
app = FastAPI(
    title="Docker Management Tool",
    description="A comprehensive Docker management platform with network, container, and monitoring capabilities",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket manager for real-time updates
websocket_manager = WebSocketManager()

# Include health check routes (no prefix for root endpoints)
app.include_router(health.router, tags=["health"])

# Include API routers
app.include_router(containers.router, prefix="/api/v1/containers", tags=["containers"])
app.include_router(networks.router, prefix="/api/v1/networks", tags=["networks"])
app.include_router(monitoring.router, prefix="/api/v1/monitoring", tags=["monitoring"])
app.include_router(scheduling.router, prefix="/api/v1/scheduling", tags=["scheduling"])
app.include_router(traffic_control.router, prefix="/api/v1/traffic", tags=["traffic-control"])

# Root and health endpoints are now handled by health.router

@app.websocket("/ws/monitoring")
async def websocket_monitoring(websocket: WebSocket):
    """WebSocket endpoint for real-time monitoring data"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            # Echo back for now - can be extended for specific commands
            await websocket.send_text(f"Echo: {data}")
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    print("Starting Docker Management Tool API...")
    # Initialize monitoring background task
    asyncio.create_task(websocket_manager.start_monitoring())

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    print("Shutting down Docker Management Tool API...")
    await websocket_manager.stop_monitoring()

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class ContainerState(str, Enum):
    CREATED = "created"
    RUNNING = "running"
    PAUSED = "paused"
    RESTARTING = "restarting"
    REMOVING = "removing"
    EXITED = "exited"
    DEAD = "dead"

class RestartPolicy(str, Enum):
    NO = "no"
    ALWAYS = "always"
    UNLESS_STOPPED = "unless-stopped"
    ON_FAILURE = "on-failure"

class ContainerCreate(BaseModel):
    name: str = Field(..., description="Container name")
    image: str = Field(..., description="Docker image name")
    command: Optional[str] = Field(None, description="Command to run in container")
    environment: Optional[Dict[str, str]] = Field(None, description="Environment variables")
    ports: Optional[Dict[str, int]] = Field(None, description="Port mappings")
    volumes: Optional[Dict[str, Dict[str, str]]] = Field(None, description="Volume mounts")
    network: Optional[str] = Field(None, description="Network to connect to")
    ip_address: Optional[str] = Field(None, description="Static IP address")
    restart_policy: RestartPolicy = Field(RestartPolicy.UNLESS_STOPPED, description="Restart policy")
    detach: bool = Field(True, description="Run container in detached mode")
    labels: Optional[Dict[str, str]] = Field(None, description="Container labels")
    working_dir: Optional[str] = Field(None, description="Working directory")
    user: Optional[str] = Field(None, description="User to run as")
    privileged: bool = Field(False, description="Run in privileged mode")

class ContainerUpdate(BaseModel):
    name: Optional[str] = Field(None, description="New container name")
    restart_policy: Optional[RestartPolicy] = Field(None, description="New restart policy")
    labels: Optional[Dict[str, str]] = Field(None, description="New labels")

class NetworkSettings(BaseModel):
    networks: Dict[str, Any] = Field(default_factory=dict)
    ip_address: Optional[str] = None
    gateway: Optional[str] = None
    mac_address: Optional[str] = None

class ContainerPort(BaseModel):
    private_port: int
    public_port: Optional[int] = None
    type: str = "tcp"
    ip: str = "0.0.0.0"

class ContainerMount(BaseModel):
    source: str
    destination: str
    mode: str = "rw"
    type: str = "bind"

class ContainerResponse(BaseModel):
    id: str = Field(..., description="Container ID")
    name: str = Field(..., description="Container name")
    image: str = Field(..., description="Image name")
    state: ContainerState = Field(..., description="Container state")
    status: str = Field(..., description="Container status")
    created: datetime = Field(..., description="Creation timestamp")
    started: Optional[datetime] = Field(None, description="Start timestamp")
    finished: Optional[datetime] = Field(None, description="Finish timestamp")
    ports: List[ContainerPort] = Field(default_factory=list, description="Port mappings")
    mounts: List[ContainerMount] = Field(default_factory=list, description="Volume mounts")
    network_settings: NetworkSettings = Field(default_factory=NetworkSettings, description="Network configuration")
    labels: Dict[str, str] = Field(default_factory=dict, description="Container labels")
    restart_policy: Dict[str, Any] = Field(default_factory=dict, description="Restart policy")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class CPUStats(BaseModel):
    cpu_percent: float = Field(..., description="CPU usage percentage")
    cpu_count: int = Field(..., description="Number of CPU cores")
    cpu_usage: Dict[str, Any] = Field(default_factory=dict, description="Detailed CPU usage")

class MemoryStats(BaseModel):
    usage: int = Field(..., description="Memory usage in bytes")
    limit: int = Field(..., description="Memory limit in bytes")
    percent: float = Field(..., description="Memory usage percentage")
    cache: int = Field(0, description="Cache memory in bytes")
    rss: int = Field(0, description="RSS memory in bytes")

class NetworkIOStats(BaseModel):
    rx_bytes: int = Field(0, description="Bytes received")
    tx_bytes: int = Field(0, description="Bytes transmitted")
    rx_packets: int = Field(0, description="Packets received")
    tx_packets: int = Field(0, description="Packets transmitted")
    rx_errors: int = Field(0, description="Receive errors")
    tx_errors: int = Field(0, description="Transmit errors")
    rx_dropped: int = Field(0, description="Dropped received packets")
    tx_dropped: int = Field(0, description="Dropped transmitted packets")

class BlockIOStats(BaseModel):
    read_bytes: int = Field(0, description="Bytes read from disk")
    write_bytes: int = Field(0, description="Bytes written to disk")
    read_ops: int = Field(0, description="Read operations")
    write_ops: int = Field(0, description="Write operations")

class ContainerStats(BaseModel):
    container_id: str = Field(..., description="Container ID")
    container_name: str = Field(..., description="Container name")
    timestamp: datetime = Field(..., description="Statistics timestamp")
    cpu: CPUStats = Field(..., description="CPU statistics")
    memory: MemoryStats = Field(..., description="Memory statistics")
    network: NetworkIOStats = Field(..., description="Network I/O statistics")
    block_io: BlockIOStats = Field(..., description="Block I/O statistics")
    pids: int = Field(0, description="Number of processes/threads")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

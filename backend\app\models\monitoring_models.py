from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

from .container_models import ContainerStats
from .network_models import NetworkStats

class AlertLevel(str, Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class SystemStats(BaseModel):
    timestamp: datetime = Field(..., description="Statistics timestamp")
    cpu_percent: float = Field(..., description="Overall CPU usage percentage")
    cpu_count: int = Field(..., description="Number of CPU cores")
    cpu_freq: Optional[float] = Field(None, description="CPU frequency in MHz")
    memory_total: int = Field(..., description="Total memory in bytes")
    memory_available: int = Field(..., description="Available memory in bytes")
    memory_used: int = Field(..., description="Used memory in bytes")
    memory_percent: float = Field(..., description="Memory usage percentage")
    swap_total: int = Field(0, description="Total swap in bytes")
    swap_used: int = Field(0, description="Used swap in bytes")
    swap_percent: float = Field(0.0, description="Swap usage percentage")
    disk_total: int = Field(..., description="Total disk space in bytes")
    disk_used: int = Field(..., description="Used disk space in bytes")
    disk_free: int = Field(..., description="Free disk space in bytes")
    disk_percent: float = Field(..., description="Disk usage percentage")
    load_avg_1: float = Field(0.0, description="1-minute load average")
    load_avg_5: float = Field(0.0, description="5-minute load average")
    load_avg_15: float = Field(0.0, description="15-minute load average")
    boot_time: datetime = Field(..., description="System boot time")
    uptime: float = Field(..., description="System uptime in seconds")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ProcessInfo(BaseModel):
    pid: int = Field(..., description="Process ID")
    name: str = Field(..., description="Process name")
    cpu_percent: float = Field(0.0, description="CPU usage percentage")
    memory_percent: float = Field(0.0, description="Memory usage percentage")
    memory_rss: int = Field(0, description="RSS memory in bytes")
    memory_vms: int = Field(0, description="VMS memory in bytes")
    status: str = Field(..., description="Process status")
    create_time: datetime = Field(..., description="Process creation time")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class DiskIOStats(BaseModel):
    device: str = Field(..., description="Disk device name")
    read_count: int = Field(0, description="Number of read operations")
    write_count: int = Field(0, description="Number of write operations")
    read_bytes: int = Field(0, description="Bytes read")
    write_bytes: int = Field(0, description="Bytes written")
    read_time: int = Field(0, description="Time spent reading (ms)")
    write_time: int = Field(0, description="Time spent writing (ms)")
    busy_time: int = Field(0, description="Time spent doing I/O (ms)")

class MonitoringAlert(BaseModel):
    id: str = Field(..., description="Alert ID")
    level: AlertLevel = Field(..., description="Alert level")
    title: str = Field(..., description="Alert title")
    message: str = Field(..., description="Alert message")
    source: str = Field(..., description="Alert source (container, system, network)")
    source_id: Optional[str] = Field(None, description="Source identifier")
    timestamp: datetime = Field(..., description="Alert timestamp")
    acknowledged: bool = Field(False, description="Alert acknowledged")
    resolved: bool = Field(False, description="Alert resolved")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class MonitoringThresholds(BaseModel):
    cpu_warning: float = Field(80.0, description="CPU warning threshold (%)")
    cpu_critical: float = Field(95.0, description="CPU critical threshold (%)")
    memory_warning: float = Field(80.0, description="Memory warning threshold (%)")
    memory_critical: float = Field(95.0, description="Memory critical threshold (%)")
    disk_warning: float = Field(80.0, description="Disk warning threshold (%)")
    disk_critical: float = Field(95.0, description="Disk critical threshold (%)")
    network_rx_warning: int = Field(100_000_000, description="Network RX warning threshold (bytes/sec)")
    network_tx_warning: int = Field(100_000_000, description="Network TX warning threshold (bytes/sec)")
    container_cpu_warning: float = Field(80.0, description="Container CPU warning threshold (%)")
    container_memory_warning: float = Field(80.0, description="Container memory warning threshold (%)")

class MonitoringResponse(BaseModel):
    timestamp: datetime = Field(..., description="Response timestamp")
    system: SystemStats = Field(..., description="System statistics")
    containers: List[ContainerStats] = Field(default_factory=list, description="Container statistics")
    networks: List[NetworkStats] = Field(default_factory=list, description="Network statistics")
    alerts: List[MonitoringAlert] = Field(default_factory=list, description="Active alerts")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class HistoricalStats(BaseModel):
    timestamp: datetime = Field(..., description="Statistics timestamp")
    cpu_percent: float = Field(..., description="CPU usage percentage")
    memory_percent: float = Field(..., description="Memory usage percentage")
    network_rx_bytes: int = Field(0, description="Network bytes received")
    network_tx_bytes: int = Field(0, description="Network bytes transmitted")
    disk_read_bytes: int = Field(0, description="Disk bytes read")
    disk_write_bytes: int = Field(0, description="Disk bytes written")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ContainerHistoricalStats(HistoricalStats):
    container_id: str = Field(..., description="Container ID")
    container_name: str = Field(..., description="Container name")

class SystemHistoricalStats(HistoricalStats):
    load_avg_1: float = Field(0.0, description="1-minute load average")
    swap_percent: float = Field(0.0, description="Swap usage percentage")
    disk_percent: float = Field(0.0, description="Disk usage percentage")

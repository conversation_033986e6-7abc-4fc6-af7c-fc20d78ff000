from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class NetworkDriver(str, Enum):
    BRIDGE = "bridge"
    HOST = "host"
    OVERLAY = "overlay"
    MACVLAN = "macvlan"
    IPVLAN = "ipvlan"
    NONE = "null"

class NetworkScope(str, Enum):
    LOCAL = "local"
    GLOBAL = "global"
    SWARM = "swarm"

class IPAMConfig(BaseModel):
    subnet: str = Field(..., description="Network subnet in CIDR notation")
    gateway: Optional[str] = Field(None, description="Gateway IP address")
    ip_range: Optional[str] = Field(None, description="IP range for allocation")
    aux_addresses: Optional[Dict[str, str]] = Field(None, description="Auxiliary addresses")

class IPAM(BaseModel):
    driver: str = Field("default", description="IPAM driver")
    config: List[IPAMConfig] = Field(default_factory=list, description="IPAM configuration")
    options: Optional[Dict[str, str]] = Field(None, description="IPAM options")

class NetworkCreate(BaseModel):
    name: str = Field(..., description="Network name")
    driver: NetworkDriver = Field(NetworkDriver.BRIDGE, description="Network driver")
    options: Optional[Dict[str, str]] = Field(None, description="Driver-specific options")
    ipam: Optional[IPAM] = Field(None, description="IP Address Management configuration")
    enable_ipv6: bool = Field(False, description="Enable IPv6")
    internal: bool = Field(False, description="Restrict external access")
    attachable: bool = Field(True, description="Allow manual container attachment")
    ingress: bool = Field(False, description="Create an ingress network")
    labels: Optional[Dict[str, str]] = Field(None, description="Network labels")

class NetworkUpdate(BaseModel):
    labels: Optional[Dict[str, str]] = Field(None, description="Updated labels")

class NetworkContainer(BaseModel):
    container_id: str = Field(..., description="Container ID")
    container_name: str = Field(..., description="Container name")
    ipv4_address: Optional[str] = Field(None, description="IPv4 address")
    ipv6_address: Optional[str] = Field(None, description="IPv6 address")
    mac_address: Optional[str] = Field(None, description="MAC address")
    endpoint_id: str = Field(..., description="Endpoint ID")

class NetworkResponse(BaseModel):
    id: str = Field(..., description="Network ID")
    name: str = Field(..., description="Network name")
    driver: str = Field(..., description="Network driver")
    scope: NetworkScope = Field(..., description="Network scope")
    created: datetime = Field(..., description="Creation timestamp")
    ipam: IPAM = Field(..., description="IPAM configuration")
    containers: List[NetworkContainer] = Field(default_factory=list, description="Connected containers")
    options: Dict[str, str] = Field(default_factory=dict, description="Network options")
    labels: Dict[str, str] = Field(default_factory=dict, description="Network labels")
    enable_ipv6: bool = Field(False, description="IPv6 enabled")
    internal: bool = Field(False, description="Internal network")
    attachable: bool = Field(True, description="Attachable network")
    ingress: bool = Field(False, description="Ingress network")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class IPAllocation(BaseModel):
    network_id: str = Field(..., description="Network ID")
    container_id: str = Field(..., description="Container ID")
    ip_address: str = Field(..., description="Allocated IP address")
    allocated_at: datetime = Field(..., description="Allocation timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class NetworkStats(BaseModel):
    interface_name: str = Field(..., description="Network interface name")
    bytes_sent: int = Field(0, description="Bytes sent")
    bytes_recv: int = Field(0, description="Bytes received")
    packets_sent: int = Field(0, description="Packets sent")
    packets_recv: int = Field(0, description="Packets received")
    errin: int = Field(0, description="Input errors")
    errout: int = Field(0, description="Output errors")
    dropin: int = Field(0, description="Input drops")
    dropout: int = Field(0, description="Output drops")
    speed: Optional[int] = Field(None, description="Interface speed in Mbps")
    mtu: Optional[int] = Field(None, description="Maximum transmission unit")
    is_up: bool = Field(True, description="Interface is up")
    
class NetworkBandwidth(BaseModel):
    interface_name: str = Field(..., description="Network interface name")
    rx_rate: float = Field(0.0, description="Receive rate in bytes/sec")
    tx_rate: float = Field(0.0, description="Transmit rate in bytes/sec")
    rx_rate_mbps: float = Field(0.0, description="Receive rate in Mbps")
    tx_rate_mbps: float = Field(0.0, description="Transmit rate in Mbps")
    timestamp: datetime = Field(..., description="Measurement timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

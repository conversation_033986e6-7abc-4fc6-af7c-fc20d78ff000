from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class ScheduleType(str, Enum):
    CONTAINER_ACTION = "container_action"
    SCRIPT_EXECUTION = "script_execution"
    CUSTOM = "custom"

class JobStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class ContainerAction(str, Enum):
    START = "start"
    STOP = "stop"
    RESTART = "restart"
    PAUSE = "pause"
    UNPAUSE = "unpause"

class ScheduleCreate(BaseModel):
    name: str = Field(..., description="Schedule name")
    schedule_type: ScheduleType = Field(..., description="Type of schedule")
    cron_expression: str = Field(..., description="Cron expression for scheduling")
    timezone: str = Field("UTC", description="Timezone for schedule")
    enabled: bool = Field(True, description="Whether schedule is enabled")
    description: Optional[str] = Field(None, description="Schedule description")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class ScheduleUpdate(BaseModel):
    name: Optional[str] = Field(None, description="Updated schedule name")
    cron_expression: Optional[str] = Field(None, description="Updated cron expression")
    timezone: Optional[str] = Field(None, description="Updated timezone")
    enabled: Optional[bool] = Field(None, description="Updated enabled status")
    description: Optional[str] = Field(None, description="Updated description")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Updated metadata")

class ContainerScheduleConfig(BaseModel):
    container_id: str = Field(..., description="Container ID")
    action: ContainerAction = Field(..., description="Action to perform")
    timeout: int = Field(30, description="Action timeout in seconds")
    script_path: Optional[str] = Field(None, description="Script to run before/after action")
    script_args: Optional[List[str]] = Field(None, description="Script arguments")
    run_script_before: bool = Field(False, description="Run script before action")
    run_script_after: bool = Field(True, description="Run script after action")

class ScriptScheduleConfig(BaseModel):
    script_path: str = Field(..., description="Path to script to execute")
    script_args: Optional[List[str]] = Field(None, description="Script arguments")
    working_directory: Optional[str] = Field(None, description="Working directory for script")
    environment: Optional[Dict[str, str]] = Field(None, description="Environment variables")
    timeout: int = Field(300, description="Script timeout in seconds")
    capture_output: bool = Field(True, description="Capture script output")

class ScheduleResponse(BaseModel):
    id: str = Field(..., description="Schedule ID")
    name: str = Field(..., description="Schedule name")
    schedule_type: ScheduleType = Field(..., description="Type of schedule")
    cron_expression: str = Field(..., description="Cron expression")
    timezone: str = Field(..., description="Timezone")
    enabled: bool = Field(..., description="Whether schedule is enabled")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    last_run: Optional[datetime] = Field(None, description="Last execution timestamp")
    next_run: Optional[datetime] = Field(None, description="Next execution timestamp")
    run_count: int = Field(0, description="Total number of executions")
    success_count: int = Field(0, description="Number of successful executions")
    failure_count: int = Field(0, description="Number of failed executions")
    description: Optional[str] = Field(None, description="Schedule description")
    config: Dict[str, Any] = Field(default_factory=dict, description="Schedule configuration")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class JobExecution(BaseModel):
    id: str = Field(..., description="Execution ID")
    schedule_id: str = Field(..., description="Schedule ID")
    schedule_name: str = Field(..., description="Schedule name")
    status: JobStatus = Field(..., description="Execution status")
    started_at: datetime = Field(..., description="Start timestamp")
    finished_at: Optional[datetime] = Field(None, description="Finish timestamp")
    duration: Optional[float] = Field(None, description="Execution duration in seconds")
    output: Optional[str] = Field(None, description="Execution output")
    error: Optional[str] = Field(None, description="Error message if failed")
    exit_code: Optional[int] = Field(None, description="Exit code")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Execution metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class CronValidation(BaseModel):
    is_valid: bool = Field(..., description="Whether cron expression is valid")
    error_message: Optional[str] = Field(None, description="Error message if invalid")
    next_runs: List[datetime] = Field(default_factory=list, description="Next 5 execution times")
    description: Optional[str] = Field(None, description="Human-readable description")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class SchedulerStats(BaseModel):
    total_schedules: int = Field(0, description="Total number of schedules")
    enabled_schedules: int = Field(0, description="Number of enabled schedules")
    running_jobs: int = Field(0, description="Number of currently running jobs")
    pending_jobs: int = Field(0, description="Number of pending jobs")
    total_executions: int = Field(0, description="Total number of executions")
    successful_executions: int = Field(0, description="Number of successful executions")
    failed_executions: int = Field(0, description="Number of failed executions")
    last_execution: Optional[datetime] = Field(None, description="Last execution timestamp")
    next_execution: Optional[datetime] = Field(None, description="Next execution timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

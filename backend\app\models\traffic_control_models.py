from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class TrafficRuleType(str, Enum):
    BANDWIDTH_LIMIT = "bandwidth_limit"
    DELAY = "delay"
    PACKET_LOSS = "packet_loss"
    PACKET_DUPLICATE = "packet_duplicate"
    PACKET_CORRUPT = "packet_corrupt"
    RATE_LIMIT = "rate_limit"
    PRIORITY = "priority"

class TrafficDirection(str, Enum):
    INGRESS = "ingress"
    EGRESS = "egress"
    BOTH = "both"

class RuleStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PENDING = "pending"

class TrafficRuleCreate(BaseModel):
    name: str = Field(..., description="Rule name")
    container_id: str = Field(..., description="Target container ID")
    rule_type: TrafficRuleType = Field(..., description="Type of traffic rule")
    direction: TrafficDirection = Field(TrafficDirection.BOTH, description="Traffic direction")
    parameters: Dict[str, Any] = Field(..., description="Rule-specific parameters")
    priority: int = Field(1, description="Rule priority (1-100)")
    enabled: bool = Field(True, description="Whether rule is enabled")
    description: Optional[str] = Field(None, description="Rule description")

class BandwidthLimit(BaseModel):
    rate: str = Field(..., description="Bandwidth rate (e.g., '100mbit', '1gbit')")
    burst: Optional[str] = Field(None, description="Burst size (e.g., '10mb')")
    ceil: Optional[str] = Field(None, description="Ceiling rate")
    buffer: Optional[str] = Field(None, description="Buffer size")

class DelayParameters(BaseModel):
    delay: str = Field(..., description="Delay amount (e.g., '100ms', '1s')")
    jitter: Optional[str] = Field(None, description="Jitter amount")
    correlation: Optional[float] = Field(None, description="Correlation percentage")
    distribution: Optional[str] = Field(None, description="Delay distribution")

class LossParameters(BaseModel):
    loss_percent: float = Field(..., description="Packet loss percentage")
    correlation: Optional[float] = Field(None, description="Loss correlation")

class DuplicateParameters(BaseModel):
    duplicate_percent: float = Field(..., description="Packet duplication percentage")
    correlation: Optional[float] = Field(None, description="Duplication correlation")

class CorruptParameters(BaseModel):
    corrupt_percent: float = Field(..., description="Packet corruption percentage")
    correlation: Optional[float] = Field(None, description="Corruption correlation")

class TrafficRule(BaseModel):
    id: str = Field(..., description="Rule ID")
    name: str = Field(..., description="Rule name")
    container_id: str = Field(..., description="Target container ID")
    container_name: Optional[str] = Field(None, description="Container name")
    rule_type: TrafficRuleType = Field(..., description="Type of traffic rule")
    direction: TrafficDirection = Field(..., description="Traffic direction")
    parameters: Dict[str, Any] = Field(..., description="Rule parameters")
    priority: int = Field(..., description="Rule priority")
    enabled: bool = Field(..., description="Whether rule is enabled")
    status: RuleStatus = Field(..., description="Rule status")
    interface: Optional[str] = Field(None, description="Network interface")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    applied_at: Optional[datetime] = Field(None, description="Last applied timestamp")
    description: Optional[str] = Field(None, description="Rule description")
    tc_commands: List[str] = Field(default_factory=list, description="Generated tc commands")
    error_message: Optional[str] = Field(None, description="Error message if status is error")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class TrafficRuleResponse(TrafficRule):
    pass

class ContainerInterface(BaseModel):
    container_id: str = Field(..., description="Container ID")
    container_name: str = Field(..., description="Container name")
    interface_name: str = Field(..., description="Interface name")
    ip_address: Optional[str] = Field(None, description="IP address")
    mac_address: Optional[str] = Field(None, description="MAC address")
    network_id: Optional[str] = Field(None, description="Network ID")
    network_name: Optional[str] = Field(None, description="Network name")
    mtu: Optional[int] = Field(None, description="Maximum transmission unit")
    is_up: bool = Field(True, description="Interface is up")

class TCCommand(BaseModel):
    command: str = Field(..., description="tc command")
    description: str = Field(..., description="Command description")
    rule_id: Optional[str] = Field(None, description="Associated rule ID")
    interface: str = Field(..., description="Target interface")
    direction: TrafficDirection = Field(..., description="Traffic direction")

class TrafficStats(BaseModel):
    interface: str = Field(..., description="Interface name")
    qdisc: str = Field(..., description="Queueing discipline")
    handle: str = Field(..., description="Handle ID")
    parent: Optional[str] = Field(None, description="Parent handle")
    bytes_sent: int = Field(0, description="Bytes sent")
    packets_sent: int = Field(0, description="Packets sent")
    drops: int = Field(0, description="Dropped packets")
    overlimits: int = Field(0, description="Overlimit packets")
    requeues: int = Field(0, description="Requeued packets")
    backlog: int = Field(0, description="Backlog bytes")
    timestamp: datetime = Field(..., description="Statistics timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class TrafficControlStatus(BaseModel):
    tc_available: bool = Field(..., description="tc command available")
    tc_version: Optional[str] = Field(None, description="tc version")
    ip_available: bool = Field(..., description="ip command available")
    ip_version: Optional[str] = Field(None, description="ip version")
    active_rules: int = Field(0, description="Number of active rules")
    total_rules: int = Field(0, description="Total number of rules")
    interfaces_with_rules: List[str] = Field(default_factory=list, description="Interfaces with active rules")
    last_error: Optional[str] = Field(None, description="Last error message")
    timestamp: datetime = Field(..., description="Status timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

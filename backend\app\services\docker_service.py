import docker
import asyncio
import logging
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timezone
import ipaddress
import json

from app.core.config import settings
from app.models.container_models import (
    ContainerResponse, ContainerStats, CPUStats, MemoryStats, 
    NetworkIOStats, BlockIOStats, ContainerState, NetworkSettings,
    ContainerPort, ContainerMount
)
from app.models.network_models import (
    NetworkResponse, NetworkContainer, IPAM, IPAMConfig, NetworkScope
)

logger = logging.getLogger(__name__)

class DockerService:
    """Service for Docker API operations"""
    
    def __init__(self):
        try:
            self.client = docker.DockerClient(
                base_url=settings.DOCKER_HOST,
                version=settings.DOCKER_API_VERSION
            )
            # Test connection
            self.client.ping()
            logger.info("Docker client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Docker client: {e}")
            raise
    
    async def list_containers(self, all: bool = True) -> List[ContainerResponse]:
        """List all containers"""
        try:
            containers = self.client.containers.list(all=all)
            result = []
            
            for container in containers:
                container_data = self._container_to_response(container)
                result.append(container_data)
            
            return result
        except Exception as e:
            logger.error(f"Error listing containers: {e}")
            raise
    
    async def get_container(self, container_id: str) -> Optional[ContainerResponse]:
        """Get container by ID or name"""
        try:
            container = self.client.containers.get(container_id)
            return self._container_to_response(container)
        except docker.errors.NotFound:
            return None
        except Exception as e:
            logger.error(f"Error getting container {container_id}: {e}")
            raise
    
    async def create_container(
        self,
        name: str,
        image: str,
        command: Optional[str] = None,
        environment: Optional[Dict[str, str]] = None,
        ports: Optional[Dict[str, int]] = None,
        volumes: Optional[Dict[str, Dict[str, str]]] = None,
        network: Optional[str] = None,
        ip_address: Optional[str] = None,
        restart_policy: str = "unless-stopped",
        detach: bool = True,
        **kwargs
    ) -> ContainerResponse:
        """Create a new container"""
        try:
            # Prepare container configuration
            container_config = {
                "image": image,
                "name": name,
                "detach": detach,
                "restart_policy": {"Name": restart_policy}
            }
            
            if command:
                container_config["command"] = command
            
            if environment:
                container_config["environment"] = environment
            
            if ports:
                container_config["ports"] = ports
            
            if volumes:
                container_config["volumes"] = volumes
            
            # Network configuration
            if network:
                if ip_address:
                    # Validate IP address
                    try:
                        ipaddress.IPv4Address(ip_address)
                    except ipaddress.AddressValueError:
                        raise ValueError(f"Invalid IP address: {ip_address}")
                    
                    container_config["networking_config"] = {
                        network: {"IPAMConfig": {"IPv4Address": ip_address}}
                    }
                else:
                    container_config["network"] = network
            
            # Add any additional kwargs
            container_config.update(kwargs)
            
            # Create container
            container = self.client.containers.create(**container_config)
            
            # Start container if detach is True
            if detach:
                container.start()
            
            # Reload to get updated info
            container.reload()
            
            return self._container_to_response(container)
            
        except Exception as e:
            logger.error(f"Error creating container {name}: {e}")
            raise
    
    async def start_container(self, container_id: str) -> Dict[str, Any]:
        """Start a container"""
        try:
            container = self.client.containers.get(container_id)
            container.start()
            return {"status": "started", "container_id": container_id}
        except docker.errors.NotFound:
            raise ValueError(f"Container {container_id} not found")
        except Exception as e:
            logger.error(f"Error starting container {container_id}: {e}")
            raise
    
    async def stop_container(self, container_id: str, timeout: int = 10) -> Dict[str, Any]:
        """Stop a container"""
        try:
            container = self.client.containers.get(container_id)
            container.stop(timeout=timeout)
            return {"status": "stopped", "container_id": container_id}
        except docker.errors.NotFound:
            raise ValueError(f"Container {container_id} not found")
        except Exception as e:
            logger.error(f"Error stopping container {container_id}: {e}")
            raise
    
    async def restart_container(self, container_id: str, timeout: int = 10) -> Dict[str, Any]:
        """Restart a container"""
        try:
            container = self.client.containers.get(container_id)
            container.restart(timeout=timeout)
            return {"status": "restarted", "container_id": container_id}
        except docker.errors.NotFound:
            raise ValueError(f"Container {container_id} not found")
        except Exception as e:
            logger.error(f"Error restarting container {container_id}: {e}")
            raise
    
    async def remove_container(
        self, 
        container_id: str, 
        force: bool = False, 
        remove_volumes: bool = False
    ) -> Dict[str, Any]:
        """Remove a container"""
        try:
            container = self.client.containers.get(container_id)
            container.remove(force=force, v=remove_volumes)
            return {"status": "removed", "container_id": container_id}
        except docker.errors.NotFound:
            raise ValueError(f"Container {container_id} not found")
        except Exception as e:
            logger.error(f"Error removing container {container_id}: {e}")
            raise
    
    async def get_container_stats(self, container_id: str) -> Optional[ContainerStats]:
        """Get real-time container statistics"""
        try:
            container = self.client.containers.get(container_id)
            
            # Get stats (stream=False for single reading)
            stats = container.stats(stream=False)
            
            return self._parse_container_stats(container_id, container.name, stats)
            
        except docker.errors.NotFound:
            return None
        except Exception as e:
            logger.error(f"Error getting stats for container {container_id}: {e}")
            raise
    
    async def get_container_logs(
        self, 
        container_id: str, 
        tail: int = 100, 
        follow: bool = False
    ) -> str:
        """Get container logs"""
        try:
            container = self.client.containers.get(container_id)
            logs = container.logs(tail=tail, follow=follow, timestamps=True)
            return logs.decode('utf-8') if isinstance(logs, bytes) else logs
        except docker.errors.NotFound:
            raise ValueError(f"Container {container_id} not found")
        except Exception as e:
            logger.error(f"Error getting logs for container {container_id}: {e}")
            raise

    # Network-related methods
    async def list_networks(self) -> List[NetworkResponse]:
        """List all Docker networks"""
        try:
            networks = self.client.networks.list()
            result = []

            for network in networks:
                network_data = self._network_to_response(network)
                result.append(network_data)

            return result
        except Exception as e:
            logger.error(f"Error listing networks: {e}")
            raise

    async def get_network(self, network_id: str) -> Optional[NetworkResponse]:
        """Get network by ID or name"""
        try:
            network = self.client.networks.get(network_id)
            return self._network_to_response(network)
        except docker.errors.NotFound:
            return None
        except Exception as e:
            logger.error(f"Error getting network {network_id}: {e}")
            raise

    async def create_network(
        self,
        name: str,
        driver: str = "bridge",
        options: Optional[Dict[str, str]] = None,
        ipam: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> NetworkResponse:
        """Create a Docker network"""
        try:
            network_config = {
                "name": name,
                "driver": driver
            }

            if options:
                network_config["options"] = options

            if ipam:
                network_config["ipam"] = ipam

            network_config.update(kwargs)

            network = self.client.networks.create(**network_config)
            return self._network_to_response(network)

        except Exception as e:
            logger.error(f"Error creating network {name}: {e}")
            raise

    async def remove_network(self, network_id: str) -> Dict[str, Any]:
        """Remove a Docker network"""
        try:
            network = self.client.networks.get(network_id)
            network.remove()
            return {"status": "removed", "network_id": network_id}
        except docker.errors.NotFound:
            raise ValueError(f"Network {network_id} not found")
        except Exception as e:
            logger.error(f"Error removing network {network_id}: {e}")
            raise

    async def connect_container_to_network(
        self,
        network_id: str,
        container_id: str,
        ip_address: Optional[str] = None
    ) -> Dict[str, Any]:
        """Connect a container to a network"""
        try:
            network = self.client.networks.get(network_id)

            connect_config = {}
            if ip_address:
                connect_config["ipv4_address"] = ip_address

            network.connect(container_id, **connect_config)
            return {
                "status": "connected",
                "network_id": network_id,
                "container_id": container_id,
                "ip_address": ip_address
            }
        except docker.errors.NotFound as e:
            raise ValueError(f"Network or container not found: {e}")
        except Exception as e:
            logger.error(f"Error connecting container {container_id} to network {network_id}: {e}")
            raise

    async def disconnect_container_from_network(
        self,
        network_id: str,
        container_id: str,
        force: bool = False
    ) -> Dict[str, Any]:
        """Disconnect a container from a network"""
        try:
            network = self.client.networks.get(network_id)
            network.disconnect(container_id, force=force)
            return {
                "status": "disconnected",
                "network_id": network_id,
                "container_id": container_id
            }
        except docker.errors.NotFound as e:
            raise ValueError(f"Network or container not found: {e}")
        except Exception as e:
            logger.error(f"Error disconnecting container {container_id} from network {network_id}: {e}")
            raise

    async def get_network_containers(self, network_id: str) -> List[NetworkContainer]:
        """Get all containers connected to a network"""
        try:
            network = self.client.networks.get(network_id)
            network.reload()

            containers = []
            for container_id, container_info in network.attrs.get("Containers", {}).items():
                container_data = NetworkContainer(
                    container_id=container_id,
                    container_name=container_info.get("Name", ""),
                    ipv4_address=container_info.get("IPv4Address", "").split("/")[0] if container_info.get("IPv4Address") else None,
                    ipv6_address=container_info.get("IPv6Address", "").split("/")[0] if container_info.get("IPv6Address") else None,
                    mac_address=container_info.get("MacAddress"),
                    endpoint_id=container_info.get("EndpointID", "")
                )
                containers.append(container_data)

            return containers
        except docker.errors.NotFound:
            raise ValueError(f"Network {network_id} not found")
        except Exception as e:
            logger.error(f"Error getting containers for network {network_id}: {e}")
            raise

    async def get_available_network_ips(self, network_id: str, limit: int = 50) -> List[str]:
        """Get available IP addresses in a network"""
        try:
            network = self.client.networks.get(network_id)
            network.reload()

            # Get network IPAM configuration
            ipam_config = network.attrs.get("IPAM", {}).get("Config", [])
            if not ipam_config:
                return []

            subnet = ipam_config[0].get("Subnet")
            if not subnet:
                return []

            # Parse subnet
            network_obj = ipaddress.IPv4Network(subnet, strict=False)

            # Get used IPs
            used_ips = set()
            for container_info in network.attrs.get("Containers", {}).values():
                ip_addr = container_info.get("IPv4Address", "").split("/")[0]
                if ip_addr:
                    used_ips.add(ip_addr)

            # Add gateway to used IPs
            gateway = ipam_config[0].get("Gateway")
            if gateway:
                used_ips.add(gateway)

            # Generate available IPs
            available_ips = []
            for ip in network_obj.hosts():
                if str(ip) not in used_ips:
                    available_ips.append(str(ip))
                    if len(available_ips) >= limit:
                        break

            return available_ips

        except docker.errors.NotFound:
            raise ValueError(f"Network {network_id} not found")
        except Exception as e:
            logger.error(f"Error getting available IPs for network {network_id}: {e}")
            raise

    # Helper methods
    def _container_to_response(self, container) -> ContainerResponse:
        """Convert Docker container object to ContainerResponse"""
        try:
            container.reload()
            attrs = container.attrs

            # Parse state
            state_data = attrs.get("State", {})
            state = ContainerState.CREATED
            if state_data.get("Running"):
                state = ContainerState.RUNNING
            elif state_data.get("Paused"):
                state = ContainerState.PAUSED
            elif state_data.get("Restarting"):
                state = ContainerState.RESTARTING
            elif state_data.get("Dead"):
                state = ContainerState.DEAD
            elif state_data.get("ExitCode", 0) != 0:
                state = ContainerState.EXITED

            # Parse timestamps
            created = datetime.fromisoformat(attrs.get("Created", "").replace("Z", "+00:00"))
            started = None
            finished = None

            if state_data.get("StartedAt"):
                started = datetime.fromisoformat(state_data["StartedAt"].replace("Z", "+00:00"))
            if state_data.get("FinishedAt") and state_data["FinishedAt"] != "0001-01-01T00:00:00Z":
                finished = datetime.fromisoformat(state_data["FinishedAt"].replace("Z", "+00:00"))

            # Parse ports
            ports = []
            port_bindings = attrs.get("NetworkSettings", {}).get("Ports", {})
            for private_port, bindings in port_bindings.items():
                port_info = private_port.split("/")
                port_num = int(port_info[0])
                port_type = port_info[1] if len(port_info) > 1 else "tcp"

                if bindings:
                    for binding in bindings:
                        ports.append(ContainerPort(
                            private_port=port_num,
                            public_port=int(binding["HostPort"]),
                            type=port_type,
                            ip=binding["HostIp"]
                        ))
                else:
                    ports.append(ContainerPort(
                        private_port=port_num,
                        type=port_type
                    ))

            # Parse mounts
            mounts = []
            for mount in attrs.get("Mounts", []):
                mounts.append(ContainerMount(
                    source=mount.get("Source", ""),
                    destination=mount.get("Destination", ""),
                    mode=mount.get("Mode", "rw"),
                    type=mount.get("Type", "bind")
                ))

            # Parse network settings
            network_settings = NetworkSettings()
            net_settings = attrs.get("NetworkSettings", {})
            if net_settings:
                network_settings.networks = net_settings.get("Networks", {})
                network_settings.ip_address = net_settings.get("IPAddress")
                network_settings.gateway = net_settings.get("Gateway")
                network_settings.mac_address = net_settings.get("MacAddress")

            return ContainerResponse(
                id=container.id,
                name=container.name.lstrip("/"),
                image=attrs.get("Config", {}).get("Image", ""),
                state=state,
                status=attrs.get("State", {}).get("Status", ""),
                created=created,
                started=started,
                finished=finished,
                ports=ports,
                mounts=mounts,
                network_settings=network_settings,
                labels=attrs.get("Config", {}).get("Labels", {}),
                restart_policy=attrs.get("HostConfig", {}).get("RestartPolicy", {})
            )

        except Exception as e:
            logger.error(f"Error converting container to response: {e}")
            raise

    def _network_to_response(self, network) -> NetworkResponse:
        """Convert Docker network object to NetworkResponse"""
        try:
            network.reload()
            attrs = network.attrs

            # Parse IPAM
            ipam_data = attrs.get("IPAM", {})
            ipam_configs = []
            for config in ipam_data.get("Config", []):
                ipam_configs.append(IPAMConfig(
                    subnet=config.get("Subnet", ""),
                    gateway=config.get("Gateway"),
                    ip_range=config.get("IPRange"),
                    aux_addresses=config.get("AuxiliaryAddresses")
                ))

            ipam = IPAM(
                driver=ipam_data.get("Driver", "default"),
                config=ipam_configs,
                options=ipam_data.get("Options")
            )

            # Parse containers
            containers = []
            for container_id, container_info in attrs.get("Containers", {}).items():
                containers.append(NetworkContainer(
                    container_id=container_id,
                    container_name=container_info.get("Name", ""),
                    ipv4_address=container_info.get("IPv4Address", "").split("/")[0] if container_info.get("IPv4Address") else None,
                    ipv6_address=container_info.get("IPv6Address", "").split("/")[0] if container_info.get("IPv6Address") else None,
                    mac_address=container_info.get("MacAddress"),
                    endpoint_id=container_info.get("EndpointID", "")
                ))

            # Parse scope
            scope = NetworkScope.LOCAL
            if attrs.get("Scope") == "global":
                scope = NetworkScope.GLOBAL
            elif attrs.get("Scope") == "swarm":
                scope = NetworkScope.SWARM

            # Parse creation time
            created = datetime.fromisoformat(attrs.get("Created", "").replace("Z", "+00:00"))

            return NetworkResponse(
                id=network.id,
                name=network.name,
                driver=attrs.get("Driver", ""),
                scope=scope,
                created=created,
                ipam=ipam,
                containers=containers,
                options=attrs.get("Options", {}),
                labels=attrs.get("Labels", {}),
                enable_ipv6=attrs.get("EnableIPv6", False),
                internal=attrs.get("Internal", False),
                attachable=attrs.get("Attachable", True),
                ingress=attrs.get("Ingress", False)
            )

        except Exception as e:
            logger.error(f"Error converting network to response: {e}")
            raise

    def _parse_container_stats(self, container_id: str, container_name: str, stats: Dict[str, Any]) -> ContainerStats:
        """Parse Docker container stats into ContainerStats model"""
        try:
            # CPU stats
            cpu_stats = stats.get("cpu_stats", {})
            precpu_stats = stats.get("precpu_stats", {})

            cpu_percent = 0.0
            if cpu_stats and precpu_stats:
                cpu_delta = cpu_stats.get("cpu_usage", {}).get("total_usage", 0) - precpu_stats.get("cpu_usage", {}).get("total_usage", 0)
                system_delta = cpu_stats.get("system_cpu_usage", 0) - precpu_stats.get("system_cpu_usage", 0)

                if system_delta > 0 and cpu_delta > 0:
                    cpu_count = cpu_stats.get("online_cpus", 1)
                    cpu_percent = (cpu_delta / system_delta) * cpu_count * 100.0

            cpu = CPUStats(
                cpu_percent=cpu_percent,
                cpu_count=cpu_stats.get("online_cpus", 1),
                cpu_usage=cpu_stats.get("cpu_usage", {})
            )

            # Memory stats
            memory_stats = stats.get("memory_stats", {})
            memory_usage = memory_stats.get("usage", 0)
            memory_limit = memory_stats.get("limit", 0)
            memory_percent = (memory_usage / memory_limit * 100.0) if memory_limit > 0 else 0.0

            memory = MemoryStats(
                usage=memory_usage,
                limit=memory_limit,
                percent=memory_percent,
                cache=memory_stats.get("stats", {}).get("cache", 0),
                rss=memory_stats.get("stats", {}).get("rss", 0)
            )

            # Network stats
            networks = stats.get("networks", {})
            network_io = NetworkIOStats()
            for interface, net_stats in networks.items():
                network_io.rx_bytes += net_stats.get("rx_bytes", 0)
                network_io.tx_bytes += net_stats.get("tx_bytes", 0)
                network_io.rx_packets += net_stats.get("rx_packets", 0)
                network_io.tx_packets += net_stats.get("tx_packets", 0)
                network_io.rx_errors += net_stats.get("rx_errors", 0)
                network_io.tx_errors += net_stats.get("tx_errors", 0)
                network_io.rx_dropped += net_stats.get("rx_dropped", 0)
                network_io.tx_dropped += net_stats.get("tx_dropped", 0)

            # Block I/O stats
            blkio_stats = stats.get("blkio_stats", {})
            block_io = BlockIOStats()

            for io_stat in blkio_stats.get("io_service_bytes_recursive", []):
                if io_stat.get("op") == "Read":
                    block_io.read_bytes += io_stat.get("value", 0)
                elif io_stat.get("op") == "Write":
                    block_io.write_bytes += io_stat.get("value", 0)

            for io_stat in blkio_stats.get("io_serviced_recursive", []):
                if io_stat.get("op") == "Read":
                    block_io.read_ops += io_stat.get("value", 0)
                elif io_stat.get("op") == "Write":
                    block_io.write_ops += io_stat.get("value", 0)

            # PIDs
            pids = stats.get("pids_stats", {}).get("current", 0)

            return ContainerStats(
                container_id=container_id,
                container_name=container_name,
                timestamp=datetime.utcnow(),
                cpu=cpu,
                memory=memory,
                network=network_io,
                block_io=block_io,
                pids=pids
            )

        except Exception as e:
            logger.error(f"Error parsing container stats: {e}")
            raise

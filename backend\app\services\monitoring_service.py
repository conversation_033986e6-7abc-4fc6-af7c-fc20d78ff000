import psutil
import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone, timedelta
import json

from app.services.docker_service import DockerService
from app.models.monitoring_models import (
    SystemStats, ContainerStats, NetworkStats, MonitoringAlert,
    AlertLevel, MonitoringThresholds, HistoricalStats
)
from app.models.network_models import NetworkBandwidth

logger = logging.getLogger(__name__)

class MonitoringService:
    """Service for system and container monitoring"""
    
    def __init__(self):
        self.docker_service = DockerService()
        self.thresholds = MonitoringThresholds()
        self.alerts: List[MonitoringAlert] = []
        self._previous_network_stats = {}
        self._previous_stats_time = None
    
    async def get_system_stats(self) -> SystemStats:
        """Get current system statistics"""
        try:
            # CPU stats
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # Memory stats
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # Disk stats
            disk = psutil.disk_usage('/')
            
            # Load average (Unix-like systems)
            load_avg = [0.0, 0.0, 0.0]
            try:
                load_avg = list(psutil.getloadavg())
            except AttributeError:
                # Windows doesn't have load average
                pass
            
            # Boot time and uptime
            boot_time = datetime.fromtimestamp(psutil.boot_time(), tz=timezone.utc)
            uptime = (datetime.now(timezone.utc) - boot_time).total_seconds()
            
            return SystemStats(
                timestamp=datetime.now(timezone.utc),
                cpu_percent=cpu_percent,
                cpu_count=cpu_count,
                cpu_freq=cpu_freq.current if cpu_freq else None,
                memory_total=memory.total,
                memory_available=memory.available,
                memory_used=memory.used,
                memory_percent=memory.percent,
                swap_total=swap.total,
                swap_used=swap.used,
                swap_percent=swap.percent,
                disk_total=disk.total,
                disk_used=disk.used,
                disk_free=disk.free,
                disk_percent=(disk.used / disk.total * 100) if disk.total > 0 else 0,
                load_avg_1=load_avg[0],
                load_avg_5=load_avg[1],
                load_avg_15=load_avg[2],
                boot_time=boot_time,
                uptime=uptime
            )
            
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            raise
    
    async def get_all_container_stats(self) -> List[ContainerStats]:
        """Get statistics for all running containers"""
        try:
            containers = await self.docker_service.list_containers(all=False)
            stats_list = []
            
            for container in containers:
                if container.state.value == "running":
                    try:
                        stats = await self.docker_service.get_container_stats(container.id)
                        if stats:
                            stats_list.append(stats)
                    except Exception as e:
                        logger.warning(f"Failed to get stats for container {container.id}: {e}")
                        continue
            
            return stats_list
            
        except Exception as e:
            logger.error(f"Error getting container stats: {e}")
            raise
    
    async def get_container_stats(self, container_id: str) -> Optional[ContainerStats]:
        """Get statistics for a specific container"""
        try:
            return await self.docker_service.get_container_stats(container_id)
        except Exception as e:
            logger.error(f"Error getting stats for container {container_id}: {e}")
            raise
    
    async def get_network_stats(self) -> List[NetworkStats]:
        """Get network interface statistics"""
        try:
            network_stats = []
            net_io = psutil.net_io_counters(pernic=True)
            
            for interface, stats in net_io.items():
                # Skip loopback and virtual interfaces
                if interface.startswith(('lo', 'docker', 'br-', 'veth')):
                    continue
                
                # Get interface info
                try:
                    addrs = psutil.net_if_addrs().get(interface, [])
                    interface_stats = psutil.net_if_stats().get(interface)
                    
                    network_stat = NetworkStats(
                        interface_name=interface,
                        bytes_sent=stats.bytes_sent,
                        bytes_recv=stats.bytes_recv,
                        packets_sent=stats.packets_sent,
                        packets_recv=stats.packets_recv,
                        errin=stats.errin,
                        errout=stats.errout,
                        dropin=stats.dropin,
                        dropout=stats.dropout,
                        speed=interface_stats.speed if interface_stats else None,
                        mtu=interface_stats.mtu if interface_stats else None,
                        is_up=interface_stats.isup if interface_stats else True
                    )
                    
                    network_stats.append(network_stat)
                    
                except Exception as e:
                    logger.warning(f"Error getting stats for interface {interface}: {e}")
                    continue
            
            return network_stats
            
        except Exception as e:
            logger.error(f"Error getting network stats: {e}")
            raise
    
    async def get_interface_stats(self, interface_name: str) -> Optional[NetworkStats]:
        """Get statistics for a specific network interface"""
        try:
            net_io = psutil.net_io_counters(pernic=True)
            
            if interface_name not in net_io:
                return None
            
            stats = net_io[interface_name]
            interface_stats = psutil.net_if_stats().get(interface_name)
            
            return NetworkStats(
                interface_name=interface_name,
                bytes_sent=stats.bytes_sent,
                bytes_recv=stats.bytes_recv,
                packets_sent=stats.packets_sent,
                packets_recv=stats.packets_recv,
                errin=stats.errin,
                errout=stats.errout,
                dropin=stats.dropin,
                dropout=stats.dropout,
                speed=interface_stats.speed if interface_stats else None,
                mtu=interface_stats.mtu if interface_stats else None,
                is_up=interface_stats.isup if interface_stats else True
            )
            
        except Exception as e:
            logger.error(f"Error getting stats for interface {interface_name}: {e}")
            raise
    
    async def get_container_bandwidth(self, container_id: str) -> Optional[NetworkBandwidth]:
        """Get real-time bandwidth usage for a container"""
        try:
            # This is a simplified implementation
            # In a real scenario, you'd need to track container network interfaces
            # and calculate bandwidth based on previous measurements
            
            current_time = datetime.now(timezone.utc)
            
            # Get container stats
            stats = await self.docker_service.get_container_stats(container_id)
            if not stats:
                return None
            
            # Calculate bandwidth (simplified - would need historical data)
            rx_rate = 0.0
            tx_rate = 0.0
            
            if container_id in self._previous_network_stats and self._previous_stats_time:
                time_delta = (current_time - self._previous_stats_time).total_seconds()
                if time_delta > 0:
                    prev_stats = self._previous_network_stats[container_id]
                    rx_rate = (stats.network.rx_bytes - prev_stats.rx_bytes) / time_delta
                    tx_rate = (stats.network.tx_bytes - prev_stats.tx_bytes) / time_delta
            
            # Store current stats for next calculation
            self._previous_network_stats[container_id] = stats.network
            self._previous_stats_time = current_time
            
            return NetworkBandwidth(
                interface_name=f"container-{container_id[:12]}",
                rx_rate=rx_rate,
                tx_rate=tx_rate,
                rx_rate_mbps=rx_rate * 8 / 1_000_000,  # Convert to Mbps
                tx_rate_mbps=tx_rate * 8 / 1_000_000,
                timestamp=current_time
            )
            
        except Exception as e:
            logger.error(f"Error getting bandwidth for container {container_id}: {e}")
            raise
    
    async def get_container_stats_history(
        self, 
        container_id: str, 
        start_time: datetime, 
        end_time: datetime
    ) -> List[HistoricalStats]:
        """Get historical statistics for a container"""
        try:
            # This would typically query a time-series database
            # For now, return empty list as placeholder
            logger.info(f"Historical stats requested for {container_id} from {start_time} to {end_time}")
            return []
            
        except Exception as e:
            logger.error(f"Error getting historical stats for container {container_id}: {e}")
            raise
    
    async def get_alerts(self) -> List[MonitoringAlert]:
        """Get current monitoring alerts"""
        try:
            # Check system thresholds
            await self._check_system_alerts()
            
            # Check container thresholds
            await self._check_container_alerts()
            
            # Return active alerts
            return [alert for alert in self.alerts if not alert.resolved]
            
        except Exception as e:
            logger.error(f"Error getting alerts: {e}")
            raise
    
    async def set_alert_thresholds(self, thresholds: Dict[str, float]) -> Dict[str, Any]:
        """Set monitoring alert thresholds"""
        try:
            for key, value in thresholds.items():
                if hasattr(self.thresholds, key):
                    setattr(self.thresholds, key, value)
            
            return {"status": "updated", "thresholds": thresholds}
            
        except Exception as e:
            logger.error(f"Error setting alert thresholds: {e}")
            raise
    
    async def _check_system_alerts(self):
        """Check system-level alerts"""
        try:
            system_stats = await self.get_system_stats()
            
            # CPU alerts
            if system_stats.cpu_percent > self.thresholds.cpu_critical:
                await self._create_alert(
                    AlertLevel.CRITICAL,
                    "High CPU Usage",
                    f"System CPU usage is {system_stats.cpu_percent:.1f}%",
                    "system"
                )
            elif system_stats.cpu_percent > self.thresholds.cpu_warning:
                await self._create_alert(
                    AlertLevel.WARNING,
                    "Elevated CPU Usage",
                    f"System CPU usage is {system_stats.cpu_percent:.1f}%",
                    "system"
                )
            
            # Memory alerts
            if system_stats.memory_percent > self.thresholds.memory_critical:
                await self._create_alert(
                    AlertLevel.CRITICAL,
                    "High Memory Usage",
                    f"System memory usage is {system_stats.memory_percent:.1f}%",
                    "system"
                )
            elif system_stats.memory_percent > self.thresholds.memory_warning:
                await self._create_alert(
                    AlertLevel.WARNING,
                    "Elevated Memory Usage",
                    f"System memory usage is {system_stats.memory_percent:.1f}%",
                    "system"
                )
            
            # Disk alerts
            if system_stats.disk_percent > self.thresholds.disk_critical:
                await self._create_alert(
                    AlertLevel.CRITICAL,
                    "High Disk Usage",
                    f"System disk usage is {system_stats.disk_percent:.1f}%",
                    "system"
                )
            elif system_stats.disk_percent > self.thresholds.disk_warning:
                await self._create_alert(
                    AlertLevel.WARNING,
                    "Elevated Disk Usage",
                    f"System disk usage is {system_stats.disk_percent:.1f}%",
                    "system"
                )
                
        except Exception as e:
            logger.error(f"Error checking system alerts: {e}")
    
    async def _check_container_alerts(self):
        """Check container-level alerts"""
        try:
            container_stats = await self.get_all_container_stats()
            
            for stats in container_stats:
                # Container CPU alerts
                if stats.cpu.cpu_percent > self.thresholds.container_cpu_warning:
                    await self._create_alert(
                        AlertLevel.WARNING,
                        "High Container CPU Usage",
                        f"Container {stats.container_name} CPU usage is {stats.cpu.cpu_percent:.1f}%",
                        "container",
                        stats.container_id
                    )
                
                # Container memory alerts
                if stats.memory.percent > self.thresholds.container_memory_warning:
                    await self._create_alert(
                        AlertLevel.WARNING,
                        "High Container Memory Usage",
                        f"Container {stats.container_name} memory usage is {stats.memory.percent:.1f}%",
                        "container",
                        stats.container_id
                    )
                    
        except Exception as e:
            logger.error(f"Error checking container alerts: {e}")
    
    async def _create_alert(
        self, 
        level: AlertLevel, 
        title: str, 
        message: str, 
        source: str, 
        source_id: Optional[str] = None
    ):
        """Create a new monitoring alert"""
        try:
            alert_id = f"{source}_{source_id or 'system'}_{int(datetime.now(timezone.utc).timestamp())}"
            
            alert = MonitoringAlert(
                id=alert_id,
                level=level,
                title=title,
                message=message,
                source=source,
                source_id=source_id,
                timestamp=datetime.now(timezone.utc),
                acknowledged=False,
                resolved=False
            )
            
            self.alerts.append(alert)
            logger.info(f"Created alert: {title} - {message}")
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")

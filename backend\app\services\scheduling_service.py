import asyncio
import logging
import subprocess
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from croniter import croniter
import json

from app.services.docker_service import DockerService
from app.models.scheduling_models import (
    ScheduleResponse, ScheduleUpdate, JobExecution, JobStatus,
    ScheduleType, ContainerAction, CronValidation, SchedulerStats
)

logger = logging.getLogger(__name__)

class SchedulingService:
    """Service for container and script scheduling"""
    
    def __init__(self):
        self.docker_service = DockerService()
        self.schedules: Dict[str, Dict[str, Any]] = {}
        self.executions: List[JobExecution] = []
        self.running_jobs: Dict[str, asyncio.Task] = {}
    
    async def list_schedules(self, enabled_only: bool = False) -> List[ScheduleResponse]:
        """List all scheduled jobs"""
        try:
            schedules = []
            for schedule_id, schedule_data in self.schedules.items():
                if enabled_only and not schedule_data.get("enabled", True):
                    continue
                
                schedule = self._schedule_data_to_response(schedule_id, schedule_data)
                schedules.append(schedule)
            
            return schedules
            
        except Exception as e:
            logger.error(f"Error listing schedules: {e}")
            raise
    
    async def get_schedule(self, schedule_id: str) -> Optional[ScheduleResponse]:
        """Get schedule details by ID"""
        try:
            if schedule_id not in self.schedules:
                return None
            
            schedule_data = self.schedules[schedule_id]
            return self._schedule_data_to_response(schedule_id, schedule_data)
            
        except Exception as e:
            logger.error(f"Error getting schedule {schedule_id}: {e}")
            raise
    
    async def create_container_schedule(
        self,
        name: str,
        container_id: str,
        action: str,
        cron_expression: str,
        timezone: str = "UTC",
        enabled: bool = True,
        script_path: Optional[str] = None,
        script_args: Optional[List[str]] = None,
        description: Optional[str] = None
    ) -> ScheduleResponse:
        """Create a new container action schedule"""
        try:
            # Validate cron expression
            validation = await self.validate_cron_expression(cron_expression)
            if not validation.is_valid:
                raise ValueError(f"Invalid cron expression: {validation.error_message}")
            
            # Validate container exists
            container = await self.docker_service.get_container(container_id)
            if not container:
                raise ValueError(f"Container {container_id} not found")
            
            schedule_id = str(uuid.uuid4())
            current_time = datetime.now(timezone.utc)
            
            schedule_data = {
                "name": name,
                "schedule_type": ScheduleType.CONTAINER_ACTION,
                "cron_expression": cron_expression,
                "timezone": timezone,
                "enabled": enabled,
                "created_at": current_time,
                "updated_at": current_time,
                "last_run": None,
                "next_run": self._calculate_next_run(cron_expression, timezone),
                "run_count": 0,
                "success_count": 0,
                "failure_count": 0,
                "description": description,
                "config": {
                    "container_id": container_id,
                    "action": action,
                    "script_path": script_path,
                    "script_args": script_args or []
                }
            }
            
            self.schedules[schedule_id] = schedule_data
            
            return self._schedule_data_to_response(schedule_id, schedule_data)
            
        except Exception as e:
            logger.error(f"Error creating container schedule: {e}")
            raise
    
    async def create_script_schedule(
        self,
        name: str,
        script_path: str,
        script_args: Optional[List[str]] = None,
        cron_expression: str = "0 0 * * *",
        timezone: str = "UTC",
        enabled: bool = True,
        working_directory: Optional[str] = None,
        environment: Optional[Dict[str, str]] = None,
        description: Optional[str] = None
    ) -> ScheduleResponse:
        """Create a new script execution schedule"""
        try:
            # Validate cron expression
            validation = await self.validate_cron_expression(cron_expression)
            if not validation.is_valid:
                raise ValueError(f"Invalid cron expression: {validation.error_message}")
            
            schedule_id = str(uuid.uuid4())
            current_time = datetime.now(timezone.utc)
            
            schedule_data = {
                "name": name,
                "schedule_type": ScheduleType.SCRIPT_EXECUTION,
                "cron_expression": cron_expression,
                "timezone": timezone,
                "enabled": enabled,
                "created_at": current_time,
                "updated_at": current_time,
                "last_run": None,
                "next_run": self._calculate_next_run(cron_expression, timezone),
                "run_count": 0,
                "success_count": 0,
                "failure_count": 0,
                "description": description,
                "config": {
                    "script_path": script_path,
                    "script_args": script_args or [],
                    "working_directory": working_directory,
                    "environment": environment or {}
                }
            }
            
            self.schedules[schedule_id] = schedule_data
            
            return self._schedule_data_to_response(schedule_id, schedule_data)
            
        except Exception as e:
            logger.error(f"Error creating script schedule: {e}")
            raise
    
    async def update_schedule(self, schedule_id: str, schedule_data: ScheduleUpdate) -> Optional[ScheduleResponse]:
        """Update an existing schedule"""
        try:
            if schedule_id not in self.schedules:
                return None
            
            current_schedule = self.schedules[schedule_id]
            
            # Update fields
            if schedule_data.name is not None:
                current_schedule["name"] = schedule_data.name
            if schedule_data.cron_expression is not None:
                # Validate new cron expression
                validation = await self.validate_cron_expression(schedule_data.cron_expression)
                if not validation.is_valid:
                    raise ValueError(f"Invalid cron expression: {validation.error_message}")
                current_schedule["cron_expression"] = schedule_data.cron_expression
                current_schedule["next_run"] = self._calculate_next_run(
                    schedule_data.cron_expression, 
                    current_schedule["timezone"]
                )
            if schedule_data.timezone is not None:
                current_schedule["timezone"] = schedule_data.timezone
                current_schedule["next_run"] = self._calculate_next_run(
                    current_schedule["cron_expression"], 
                    schedule_data.timezone
                )
            if schedule_data.enabled is not None:
                current_schedule["enabled"] = schedule_data.enabled
            if schedule_data.description is not None:
                current_schedule["description"] = schedule_data.description
            if schedule_data.metadata is not None:
                current_schedule.setdefault("metadata", {}).update(schedule_data.metadata)
            
            current_schedule["updated_at"] = datetime.now(timezone.utc)
            
            return self._schedule_data_to_response(schedule_id, current_schedule)
            
        except Exception as e:
            logger.error(f"Error updating schedule {schedule_id}: {e}")
            raise
    
    async def delete_schedule(self, schedule_id: str) -> bool:
        """Delete a schedule"""
        try:
            if schedule_id not in self.schedules:
                return False
            
            # Cancel any running job
            if schedule_id in self.running_jobs:
                self.running_jobs[schedule_id].cancel()
                del self.running_jobs[schedule_id]
            
            del self.schedules[schedule_id]
            return True
            
        except Exception as e:
            logger.error(f"Error deleting schedule {schedule_id}: {e}")
            raise
    
    async def enable_schedule(self, schedule_id: str) -> bool:
        """Enable a schedule"""
        try:
            if schedule_id not in self.schedules:
                return False
            
            self.schedules[schedule_id]["enabled"] = True
            self.schedules[schedule_id]["updated_at"] = datetime.now(timezone.utc)
            return True
            
        except Exception as e:
            logger.error(f"Error enabling schedule {schedule_id}: {e}")
            raise
    
    async def disable_schedule(self, schedule_id: str) -> bool:
        """Disable a schedule"""
        try:
            if schedule_id not in self.schedules:
                return False
            
            self.schedules[schedule_id]["enabled"] = False
            self.schedules[schedule_id]["updated_at"] = datetime.now(timezone.utc)
            
            # Cancel any running job
            if schedule_id in self.running_jobs:
                self.running_jobs[schedule_id].cancel()
                del self.running_jobs[schedule_id]
            
            return True
            
        except Exception as e:
            logger.error(f"Error disabling schedule {schedule_id}: {e}")
            raise
    
    async def execute_schedule_now(self, schedule_id: str) -> Dict[str, Any]:
        """Execute a schedule immediately"""
        try:
            if schedule_id not in self.schedules:
                raise ValueError(f"Schedule {schedule_id} not found")
            
            schedule_data = self.schedules[schedule_id]
            execution_id = str(uuid.uuid4())
            
            # Create execution record
            execution = JobExecution(
                id=execution_id,
                schedule_id=schedule_id,
                schedule_name=schedule_data["name"],
                status=JobStatus.PENDING,
                started_at=datetime.now(timezone.utc)
            )
            
            self.executions.append(execution)
            
            # Execute the job
            task = asyncio.create_task(self._execute_job(schedule_id, execution_id))
            self.running_jobs[execution_id] = task
            
            return {"execution_id": execution_id, "status": "started"}
            
        except Exception as e:
            logger.error(f"Error executing schedule {schedule_id}: {e}")
            raise
    
    async def get_schedule_executions(self, schedule_id: str, limit: int = 50) -> List[JobExecution]:
        """Get execution history for a schedule"""
        try:
            executions = [
                exec for exec in self.executions 
                if exec.schedule_id == schedule_id
            ]
            
            # Sort by start time, most recent first
            executions.sort(key=lambda x: x.started_at, reverse=True)
            
            return executions[:limit]
            
        except Exception as e:
            logger.error(f"Error getting executions for schedule {schedule_id}: {e}")
            raise
    
    async def get_recent_executions(self, limit: int = 100) -> List[JobExecution]:
        """Get recent job executions across all schedules"""
        try:
            # Sort by start time, most recent first
            executions = sorted(self.executions, key=lambda x: x.started_at, reverse=True)
            return executions[:limit]
            
        except Exception as e:
            logger.error(f"Error getting recent executions: {e}")
            raise
    
    async def validate_cron_expression(self, cron_expression: str) -> CronValidation:
        """Validate a cron expression and show next execution times"""
        try:
            try:
                cron = croniter(cron_expression, datetime.now())
                next_runs = []
                
                for _ in range(5):
                    next_run = cron.get_next(datetime)
                    next_runs.append(next_run.replace(tzinfo=timezone.utc))
                
                return CronValidation(
                    is_valid=True,
                    next_runs=next_runs,
                    description=f"Cron expression: {cron_expression}"
                )
                
            except Exception as e:
                return CronValidation(
                    is_valid=False,
                    error_message=str(e),
                    next_runs=[],
                    description=None
                )
                
        except Exception as e:
            logger.error(f"Error validating cron expression: {e}")
            raise

    # Helper methods
    def _calculate_next_run(self, cron_expression: str, timezone_str: str) -> Optional[datetime]:
        """Calculate next run time for a cron expression"""
        try:
            cron = croniter(cron_expression, datetime.now())
            next_run = cron.get_next(datetime)
            return next_run.replace(tzinfo=timezone.utc)
        except Exception as e:
            logger.error(f"Error calculating next run: {e}")
            return None

    def _schedule_data_to_response(self, schedule_id: str, schedule_data: Dict[str, Any]) -> ScheduleResponse:
        """Convert schedule data to ScheduleResponse"""
        return ScheduleResponse(
            id=schedule_id,
            name=schedule_data["name"],
            schedule_type=schedule_data["schedule_type"],
            cron_expression=schedule_data["cron_expression"],
            timezone=schedule_data["timezone"],
            enabled=schedule_data["enabled"],
            created_at=schedule_data["created_at"],
            updated_at=schedule_data["updated_at"],
            last_run=schedule_data.get("last_run"),
            next_run=schedule_data.get("next_run"),
            run_count=schedule_data.get("run_count", 0),
            success_count=schedule_data.get("success_count", 0),
            failure_count=schedule_data.get("failure_count", 0),
            description=schedule_data.get("description"),
            config=schedule_data.get("config", {}),
            metadata=schedule_data.get("metadata", {})
        )

    async def _execute_job(self, schedule_id: str, execution_id: str):
        """Execute a scheduled job"""
        try:
            schedule_data = self.schedules[schedule_id]
            execution = next((e for e in self.executions if e.id == execution_id), None)

            if not execution:
                logger.error(f"Execution {execution_id} not found")
                return

            execution.status = JobStatus.RUNNING
            start_time = datetime.now(timezone.utc)

            try:
                if schedule_data["schedule_type"] == ScheduleType.CONTAINER_ACTION:
                    await self._execute_container_action(schedule_data, execution)
                elif schedule_data["schedule_type"] == ScheduleType.SCRIPT_EXECUTION:
                    await self._execute_script(schedule_data, execution)

                execution.status = JobStatus.SUCCESS
                schedule_data["success_count"] += 1

            except Exception as e:
                execution.status = JobStatus.FAILED
                execution.error = str(e)
                schedule_data["failure_count"] += 1
                logger.error(f"Job execution failed: {e}")

            finally:
                execution.finished_at = datetime.now(timezone.utc)
                execution.duration = (execution.finished_at - start_time).total_seconds()
                schedule_data["run_count"] += 1
                schedule_data["last_run"] = start_time
                schedule_data["next_run"] = self._calculate_next_run(
                    schedule_data["cron_expression"],
                    schedule_data["timezone"]
                )

                # Remove from running jobs
                if execution_id in self.running_jobs:
                    del self.running_jobs[execution_id]

        except Exception as e:
            logger.error(f"Error in job execution: {e}")

    async def _execute_container_action(self, schedule_data: Dict[str, Any], execution: JobExecution):
        """Execute a container action"""
        config = schedule_data["config"]
        container_id = config["container_id"]
        action = config["action"]

        # Execute pre-script if specified
        if config.get("script_path") and config.get("run_script_before", False):
            await self._run_script(config["script_path"], config.get("script_args", []))

        # Execute container action
        if action == ContainerAction.START:
            result = await self.docker_service.start_container(container_id)
        elif action == ContainerAction.STOP:
            result = await self.docker_service.stop_container(container_id)
        elif action == ContainerAction.RESTART:
            result = await self.docker_service.restart_container(container_id)
        else:
            raise ValueError(f"Unsupported container action: {action}")

        execution.output = json.dumps(result)

        # Execute post-script if specified
        if config.get("script_path") and config.get("run_script_after", True):
            await self._run_script(config["script_path"], config.get("script_args", []))

    async def _execute_script(self, schedule_data: Dict[str, Any], execution: JobExecution):
        """Execute a script"""
        config = schedule_data["config"]
        script_path = config["script_path"]
        script_args = config.get("script_args", [])
        working_dir = config.get("working_directory")
        env = config.get("environment", {})

        output = await self._run_script(script_path, script_args, working_dir, env)
        execution.output = output

    async def _run_script(
        self,
        script_path: str,
        args: List[str] = None,
        working_dir: str = None,
        env: Dict[str, str] = None
    ) -> str:
        """Run a script and return output"""
        try:
            cmd = [script_path] + (args or [])

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT,
                cwd=working_dir,
                env=env
            )

            stdout, _ = await process.communicate()

            if process.returncode != 0:
                raise subprocess.CalledProcessError(process.returncode, cmd, stdout)

            return stdout.decode('utf-8') if stdout else ""

        except Exception as e:
            logger.error(f"Error running script {script_path}: {e}")
            raise

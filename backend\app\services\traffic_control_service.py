import asyncio
import subprocess
import logging
import uuid
import re
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from app.services.docker_service import DockerService
from app.models.traffic_control_models import (
    TrafficRule, TrafficRuleResponse, TrafficRuleType, TrafficDirection,
    RuleStatus, ContainerInterface, TCCommand, TrafficStats, TrafficControlStatus
)
from app.core.config import settings

logger = logging.getLogger(__name__)

class TrafficControlService:
    """Service for traffic control and bandwidth management"""
    
    def __init__(self):
        self.docker_service = DockerService()
        self.rules: Dict[str, TrafficRule] = {}
        self.tc_binary = settings.TC_BINARY_PATH
        self.ip_binary = settings.IP_BINARY_PATH
    
    async def list_rules(self, container_id: Optional[str] = None) -> List[TrafficRuleResponse]:
        """List all traffic control rules"""
        try:
            rules = []
            for rule in self.rules.values():
                if container_id is None or rule.container_id == container_id:
                    rules.append(TrafficRuleResponse(**rule.dict()))
            
            return rules
            
        except Exception as e:
            logger.error(f"Error listing traffic rules: {e}")
            raise
    
    async def get_rule(self, rule_id: str) -> Optional[TrafficRuleResponse]:
        """Get traffic rule details by ID"""
        try:
            if rule_id not in self.rules:
                return None
            
            rule = self.rules[rule_id]
            return TrafficRuleResponse(**rule.dict())
            
        except Exception as e:
            logger.error(f"Error getting traffic rule {rule_id}: {e}")
            raise
    
    async def create_bandwidth_limit(
        self,
        container_id: str,
        interface: Optional[str] = None,
        ingress_rate: Optional[str] = None,
        egress_rate: Optional[str] = None,
        burst: Optional[str] = None,
        description: Optional[str] = None
    ) -> TrafficRuleResponse:
        """Create bandwidth limiting rule for a container"""
        try:
            # Validate container exists
            container = await self.docker_service.get_container(container_id)
            if not container:
                raise ValueError(f"Container {container_id} not found")
            
            # Get container interface if not provided
            if not interface:
                interface_info = await self.get_container_interface(container_id)
                interface = interface_info.get("interface_name")
                if not interface:
                    raise ValueError(f"Could not determine interface for container {container_id}")
            
            rule_id = str(uuid.uuid4())
            current_time = datetime.now(timezone.utc)
            
            # Build parameters
            parameters = {}
            if ingress_rate:
                parameters["ingress_rate"] = ingress_rate
            if egress_rate:
                parameters["egress_rate"] = egress_rate
            if burst:
                parameters["burst"] = burst
            
            rule = TrafficRule(
                id=rule_id,
                name=f"Bandwidth limit for {container.name}",
                container_id=container_id,
                container_name=container.name,
                rule_type=TrafficRuleType.BANDWIDTH_LIMIT,
                direction=TrafficDirection.BOTH,
                parameters=parameters,
                priority=1,
                enabled=True,
                status=RuleStatus.INACTIVE,
                interface=interface,
                created_at=current_time,
                updated_at=current_time,
                description=description,
                tc_commands=[]
            )
            
            # Generate tc commands
            rule.tc_commands = await self._generate_bandwidth_commands(rule)
            
            self.rules[rule_id] = rule
            
            return TrafficRuleResponse(**rule.dict())
            
        except Exception as e:
            logger.error(f"Error creating bandwidth limit: {e}")
            raise
    
    async def create_shaping_rule(
        self,
        container_id: str,
        interface: Optional[str] = None,
        rule_type: str = "delay",
        parameters: Dict[str, Any] = None,
        priority: int = 1,
        description: Optional[str] = None
    ) -> TrafficRuleResponse:
        """Create a traffic shaping rule"""
        try:
            # Validate container exists
            container = await self.docker_service.get_container(container_id)
            if not container:
                raise ValueError(f"Container {container_id} not found")
            
            # Get container interface if not provided
            if not interface:
                interface_info = await self.get_container_interface(container_id)
                interface = interface_info.get("interface_name")
                if not interface:
                    raise ValueError(f"Could not determine interface for container {container_id}")
            
            rule_id = str(uuid.uuid4())
            current_time = datetime.now(timezone.utc)
            
            # Map rule type
            traffic_rule_type = TrafficRuleType.DELAY
            if rule_type == "loss":
                traffic_rule_type = TrafficRuleType.PACKET_LOSS
            elif rule_type == "duplicate":
                traffic_rule_type = TrafficRuleType.PACKET_DUPLICATE
            elif rule_type == "corrupt":
                traffic_rule_type = TrafficRuleType.PACKET_CORRUPT
            
            rule = TrafficRule(
                id=rule_id,
                name=f"{rule_type.title()} rule for {container.name}",
                container_id=container_id,
                container_name=container.name,
                rule_type=traffic_rule_type,
                direction=TrafficDirection.BOTH,
                parameters=parameters or {},
                priority=priority,
                enabled=True,
                status=RuleStatus.INACTIVE,
                interface=interface,
                created_at=current_time,
                updated_at=current_time,
                description=description,
                tc_commands=[]
            )
            
            # Generate tc commands
            rule.tc_commands = await self._generate_shaping_commands(rule)
            
            self.rules[rule_id] = rule
            
            return TrafficRuleResponse(**rule.dict())
            
        except Exception as e:
            logger.error(f"Error creating shaping rule: {e}")
            raise
    
    async def delete_rule(self, rule_id: str) -> bool:
        """Delete a traffic control rule"""
        try:
            if rule_id not in self.rules:
                return False
            
            rule = self.rules[rule_id]
            
            # Remove rule if it's active
            if rule.status == RuleStatus.ACTIVE:
                await self.remove_rule(rule_id)
            
            del self.rules[rule_id]
            return True
            
        except Exception as e:
            logger.error(f"Error deleting rule {rule_id}: {e}")
            raise
    
    async def apply_rule(self, rule_id: str) -> Dict[str, Any]:
        """Apply a traffic control rule"""
        try:
            if rule_id not in self.rules:
                raise ValueError(f"Rule {rule_id} not found")
            
            rule = self.rules[rule_id]
            
            if not rule.enabled:
                raise ValueError(f"Rule {rule_id} is disabled")
            
            # Execute tc commands
            executed_commands = []
            for command in rule.tc_commands:
                try:
                    result = await self._execute_tc_command(command)
                    executed_commands.append({"command": command, "result": result})
                except Exception as e:
                    rule.status = RuleStatus.ERROR
                    rule.error_message = str(e)
                    raise
            
            rule.status = RuleStatus.ACTIVE
            rule.applied_at = datetime.now(timezone.utc)
            rule.error_message = None
            
            return {
                "status": "applied",
                "commands": executed_commands
            }
            
        except Exception as e:
            logger.error(f"Error applying rule {rule_id}: {e}")
            raise
    
    async def remove_rule(self, rule_id: str) -> Dict[str, Any]:
        """Remove a traffic control rule from the system"""
        try:
            if rule_id not in self.rules:
                raise ValueError(f"Rule {rule_id} not found")
            
            rule = self.rules[rule_id]
            
            # Generate removal commands
            removal_commands = await self._generate_removal_commands(rule)
            
            # Execute removal commands
            executed_commands = []
            for command in removal_commands:
                try:
                    result = await self._execute_tc_command(command)
                    executed_commands.append({"command": command, "result": result})
                except Exception as e:
                    logger.warning(f"Failed to execute removal command: {e}")
                    # Continue with other commands
            
            rule.status = RuleStatus.INACTIVE
            rule.applied_at = None
            rule.error_message = None
            
            return {
                "status": "removed",
                "commands": executed_commands
            }
            
        except Exception as e:
            logger.error(f"Error removing rule {rule_id}: {e}")
            raise
    
    async def get_container_interface(self, container_id: str) -> Dict[str, Any]:
        """Get network interface information for a container"""
        try:
            container = await self.docker_service.get_container(container_id)
            if not container:
                raise ValueError(f"Container {container_id} not found")
            
            # Get container network settings
            networks = container.network_settings.networks
            
            interface_info = {
                "container_id": container_id,
                "container_name": container.name,
                "interfaces": []
            }
            
            for network_name, network_config in networks.items():
                interface_info["interfaces"].append({
                    "network": network_name,
                    "ip_address": network_config.get("IPAddress"),
                    "mac_address": network_config.get("MacAddress"),
                    "gateway": network_config.get("Gateway")
                })
            
            # For simplicity, return the first interface name
            if interface_info["interfaces"]:
                interface_info["interface_name"] = f"veth{container_id[:12]}"
            
            return interface_info
            
        except Exception as e:
            logger.error(f"Error getting container interface {container_id}: {e}")
            raise
    
    async def get_container_current_rules(self, container_id: str) -> List[Dict[str, Any]]:
        """Get currently applied traffic control rules for a container"""
        try:
            active_rules = []
            for rule in self.rules.values():
                if rule.container_id == container_id and rule.status == RuleStatus.ACTIVE:
                    active_rules.append({
                        "rule_id": rule.id,
                        "name": rule.name,
                        "rule_type": rule.rule_type,
                        "parameters": rule.parameters,
                        "applied_at": rule.applied_at
                    })
            
            return active_rules
            
        except Exception as e:
            logger.error(f"Error getting current rules for container {container_id}: {e}")
            raise
    
    async def clear_container_rules(self, container_id: str) -> Dict[str, Any]:
        """Clear all traffic control rules for a container"""
        try:
            container_rules = [
                rule for rule in self.rules.values() 
                if rule.container_id == container_id and rule.status == RuleStatus.ACTIVE
            ]
            
            executed_commands = []
            for rule in container_rules:
                try:
                    result = await self.remove_rule(rule.id)
                    executed_commands.extend(result.get("commands", []))
                except Exception as e:
                    logger.warning(f"Failed to remove rule {rule.id}: {e}")
            
            return {
                "status": "cleared",
                "rules_cleared": len(container_rules),
                "commands": executed_commands
            }
            
        except Exception as e:
            logger.error(f"Error clearing rules for container {container_id}: {e}")
            raise
    
    async def preview_rule_commands(self, rule_id: str) -> List[str]:
        """Preview the tc commands that would be executed for a rule"""
        try:
            if rule_id not in self.rules:
                raise ValueError(f"Rule {rule_id} not found")
            
            rule = self.rules[rule_id]
            return rule.tc_commands
            
        except Exception as e:
            logger.error(f"Error previewing commands for rule {rule_id}: {e}")
            raise
    
    async def get_system_status(self) -> TrafficControlStatus:
        """Get overall traffic control system status"""
        try:
            # Check if tc and ip commands are available
            tc_available = await self._check_command_availability(self.tc_binary)
            ip_available = await self._check_command_availability(self.ip_binary)
            
            # Get tc version
            tc_version = None
            ip_version = None
            
            if tc_available:
                try:
                    result = await self._execute_command([self.tc_binary, "-V"])
                    tc_version = result.strip()
                except:
                    pass
            
            if ip_available:
                try:
                    result = await self._execute_command([self.ip_binary, "-V"])
                    ip_version = result.strip()
                except:
                    pass
            
            # Count rules
            active_rules = sum(1 for rule in self.rules.values() if rule.status == RuleStatus.ACTIVE)
            total_rules = len(self.rules)
            
            # Get interfaces with rules
            interfaces_with_rules = list(set(
                rule.interface for rule in self.rules.values() 
                if rule.status == RuleStatus.ACTIVE and rule.interface
            ))
            
            return TrafficControlStatus(
                tc_available=tc_available,
                tc_version=tc_version,
                ip_available=ip_available,
                ip_version=ip_version,
                active_rules=active_rules,
                total_rules=total_rules,
                interfaces_with_rules=interfaces_with_rules,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            raise

    # Helper methods
    async def _generate_bandwidth_commands(self, rule: TrafficRule) -> List[str]:
        """Generate tc commands for bandwidth limiting"""
        commands = []
        interface = rule.interface

        # Root qdisc
        commands.append(f"{self.tc_binary} qdisc add dev {interface} root handle 1: htb default 30")

        # Class for bandwidth limiting
        if rule.parameters.get("egress_rate"):
            rate = rule.parameters["egress_rate"]
            burst = rule.parameters.get("burst", "10k")
            commands.append(f"{self.tc_binary} class add dev {interface} parent 1: classid 1:1 htb rate {rate} burst {burst}")

        # Ingress limiting (using ifb)
        if rule.parameters.get("ingress_rate"):
            rate = rule.parameters["ingress_rate"]
            commands.append(f"{self.tc_binary} qdisc add dev {interface} handle ffff: ingress")
            commands.append(f"{self.tc_binary} filter add dev {interface} parent ffff: protocol ip prio 50 u32 match ip src 0.0.0.0/0 police rate {rate} burst 10k drop flowid :1")

        return commands

    async def _generate_shaping_commands(self, rule: TrafficRule) -> List[str]:
        """Generate tc commands for traffic shaping"""
        commands = []
        interface = rule.interface

        if rule.rule_type == TrafficRuleType.DELAY:
            delay = rule.parameters.get("delay", "100ms")
            jitter = rule.parameters.get("jitter", "")
            correlation = rule.parameters.get("correlation", "")

            cmd = f"{self.tc_binary} qdisc add dev {interface} root netem delay {delay}"
            if jitter:
                cmd += f" {jitter}"
            if correlation:
                cmd += f" {correlation}%"

            commands.append(cmd)

        elif rule.rule_type == TrafficRuleType.PACKET_LOSS:
            loss_percent = rule.parameters.get("loss_percent", 1.0)
            correlation = rule.parameters.get("correlation", "")

            cmd = f"{self.tc_binary} qdisc add dev {interface} root netem loss {loss_percent}%"
            if correlation:
                cmd += f" {correlation}%"

            commands.append(cmd)

        elif rule.rule_type == TrafficRuleType.PACKET_DUPLICATE:
            duplicate_percent = rule.parameters.get("duplicate_percent", 1.0)
            correlation = rule.parameters.get("correlation", "")

            cmd = f"{self.tc_binary} qdisc add dev {interface} root netem duplicate {duplicate_percent}%"
            if correlation:
                cmd += f" {correlation}%"

            commands.append(cmd)

        elif rule.rule_type == TrafficRuleType.PACKET_CORRUPT:
            corrupt_percent = rule.parameters.get("corrupt_percent", 1.0)
            correlation = rule.parameters.get("correlation", "")

            cmd = f"{self.tc_binary} qdisc add dev {interface} root netem corrupt {corrupt_percent}%"
            if correlation:
                cmd += f" {correlation}%"

            commands.append(cmd)

        return commands

    async def _generate_removal_commands(self, rule: TrafficRule) -> List[str]:
        """Generate tc commands to remove a rule"""
        commands = []
        interface = rule.interface

        # Remove root qdisc (this removes all associated classes and filters)
        commands.append(f"{self.tc_binary} qdisc del dev {interface} root")

        # Remove ingress qdisc if it exists
        if rule.parameters.get("ingress_rate"):
            commands.append(f"{self.tc_binary} qdisc del dev {interface} ingress")

        return commands

    async def _execute_tc_command(self, command: str) -> str:
        """Execute a tc command"""
        try:
            return await self._execute_command(command.split())
        except Exception as e:
            logger.error(f"Error executing tc command '{command}': {e}")
            raise

    async def _execute_command(self, cmd: List[str]) -> str:
        """Execute a system command"""
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_msg = stderr.decode('utf-8') if stderr else "Unknown error"
                raise subprocess.CalledProcessError(process.returncode, cmd, error_msg)

            return stdout.decode('utf-8') if stdout else ""

        except Exception as e:
            logger.error(f"Error executing command {' '.join(cmd)}: {e}")
            raise

    async def _check_command_availability(self, command_path: str) -> bool:
        """Check if a command is available"""
        try:
            process = await asyncio.create_subprocess_exec(
                "which", command_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            await process.communicate()
            return process.returncode == 0

        except Exception:
            return False

#!/bin/bash

# Docker Management Tool - Backend Only Deployment Script
# Use this script to deploy backend instances on remote servers

set -e

echo "🐳 Docker Management Tool - Backend Deployment"
echo "=============================================="

# Configuration with defaults
BACKEND_PORT=${BACKEND_PORT:-8000}
REDIS_PORT=${REDIS_PORT:-6379}
SERVER_NAME=${SERVER_NAME:-"Docker-Server-$(hostname)"}
SERVER_DESCRIPTION=${SERVER_DESCRIPTION:-"Docker Management Server on $(hostname)"}
SECRET_KEY=${SECRET_KEY:-"your-secret-key-$(date +%s)"}

echo "📋 Deployment Configuration:"
echo "   Backend Port: $BACKEND_PORT"
echo "   Redis Port: $REDIS_PORT"
echo "   Server Name: $SERVER_NAME"
echo "   Server Description: $SERVER_DESCRIPTION"
echo "   Host: $(hostname)"
echo "   IP: $(hostname -I | awk '{print $1}')"
echo ""

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "❌ Port $port is already in use"
        return 1
    fi
    return 0
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            echo "✅ $service_name is ready"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to start within expected time"
    return 1
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check port availability
echo "🔍 Checking port availability..."
check_port $BACKEND_PORT || exit 1
check_port $REDIS_PORT || exit 1
echo "✅ Ports are available"

# Create environment file
echo "📝 Creating environment configuration..."
cat > .env << EOF
# Backend Configuration
BACKEND_PORT=$BACKEND_PORT
REDIS_PORT=$REDIS_PORT
SECRET_KEY=$SECRET_KEY
SERVER_NAME=$SERVER_NAME
SERVER_DESCRIPTION=$SERVER_DESCRIPTION

# Docker Configuration
DOCKER_HOST=unix:///var/run/docker.sock
DOCKER_API_VERSION=auto

# Redis Configuration
REDIS_HOST=redis
REDIS_DB=0

# Monitoring Configuration
MONITORING_INTERVAL=5
MAX_WEBSOCKET_CONNECTIONS=100

# Scheduling Configuration
SCHEDULER_TIMEZONE=UTC
MAX_CONCURRENT_JOBS=10

# Traffic Control Configuration
TC_BINARY_PATH=/sbin/tc
IP_BINARY_PATH=/sbin/ip
IPTABLES_BINARY_PATH=/usr/sbin/iptables

# Logging Configuration
LOG_LEVEL=INFO
EOF

echo "✅ Environment file created"

# Create backend environment file if it doesn't exist
if [ ! -f backend/.env ]; then
    echo "📝 Creating backend environment file..."
    cp backend/.env.example backend/.env
    
    # Update with current configuration
    sed -i "s/SECRET_KEY=.*/SECRET_KEY=$SECRET_KEY/" backend/.env
    sed -i "s/REDIS_HOST=.*/REDIS_HOST=redis/" backend/.env
    sed -i "s/REDIS_PORT=.*/REDIS_PORT=6379/" backend/.env
fi

# Deploy using backend-only compose file
echo "🔨 Building and starting backend services..."
docker-compose -f docker-compose.backend-only.yml up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check backend service
if wait_for_service "http://localhost:$BACKEND_PORT/health" "Backend API"; then
    echo "✅ Backend API is running at http://localhost:$BACKEND_PORT"
    echo "📚 API Documentation: http://localhost:$BACKEND_PORT/docs"
else
    echo "⚠️  Backend API is not responding yet"
fi

# Check Redis service
if docker-compose -f docker-compose.backend-only.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis is running"
else
    echo "⚠️  Redis is not responding"
fi

# Get server information
echo ""
echo "🎉 Backend Docker Management Server is running!"
echo ""
echo "📋 Server Information:"
echo "   Name: $SERVER_NAME"
echo "   Description: $SERVER_DESCRIPTION"
echo "   Host: $(hostname)"
echo "   IP Address: $(hostname -I | awk '{print $1}')"
echo "   Backend API: http://$(hostname -I | awk '{print $1}'):$BACKEND_PORT"
echo "   Health Check: http://$(hostname -I | awk '{print $1}'):$BACKEND_PORT/health"
echo "   API Documentation: http://$(hostname -I | awk '{print $1}'):$BACKEND_PORT/docs"
echo ""
echo "📋 Management Commands:"
echo "   View logs: docker-compose -f docker-compose.backend-only.yml logs -f"
echo "   Stop: docker-compose -f docker-compose.backend-only.yml down"
echo "   Restart: docker-compose -f docker-compose.backend-only.yml restart"
echo ""
echo "🔧 Frontend Configuration:"
echo "   To connect from the frontend, add this server configuration:"
echo "   {"
echo "     \"name\": \"$SERVER_NAME\","
echo "     \"url\": \"http://$(hostname -I | awk '{print $1}'):$BACKEND_PORT\","
echo "     \"description\": \"$SERVER_DESCRIPTION\""
echo "   }"
echo ""

# Show container status
echo "📊 Container Status:"
docker-compose -f docker-compose.backend-only.yml ps

echo ""
echo "✨ Backend deployment complete!"

#!/bin/bash

# Docker Management Tool - Distributed Deployment Script

set -e

echo "🚀 Docker Management Tool - Distributed Deployment"
echo "=================================================="

# Configuration
FRONTEND_PORT=${FRONTEND_PORT:-3000}
BACKEND_PORTS=${BACKEND_PORTS:-"8000,8001,8002"}
REDIS_PORTS=${REDIS_PORTS:-"6379,6380,6381"}

# Parse backend ports
IFS=',' read -ra BACKEND_PORT_ARRAY <<< "$BACKEND_PORTS"
IFS=',' read -ra REDIS_PORT_ARRAY <<< "$REDIS_PORTS"

echo "📋 Deployment Configuration:"
echo "   Frontend Port: $FRONTEND_PORT"
echo "   Backend Ports: $BACKEND_PORTS"
echo "   Redis Ports: $REDIS_PORTS"
echo ""

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "❌ Port $port is already in use"
        return 1
    fi
    return 0
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            echo "✅ $service_name is ready"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to start within expected time"
    return 1
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check port availability
echo "🔍 Checking port availability..."
check_port $FRONTEND_PORT || exit 1

for port in "${BACKEND_PORT_ARRAY[@]}"; do
    check_port $port || exit 1
done

for port in "${REDIS_PORT_ARRAY[@]}"; do
    check_port $port || exit 1
done

echo "✅ All ports are available"

# Create environment files if they don't exist
if [ ! -f backend/.env ]; then
    echo "📝 Creating backend environment file..."
    cp backend/.env.example backend/.env
fi

if [ ! -f frontend/.env ]; then
    echo "📝 Creating frontend environment file..."
    cp frontend/.env.example frontend/.env
fi

# Deploy using distributed compose file
echo "🔨 Building and starting distributed services..."
docker-compose -f docker-compose.distributed.yml up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check backend services
for i in "${!BACKEND_PORT_ARRAY[@]}"; do
    port="${BACKEND_PORT_ARRAY[$i]}"
    server_num=$((i + 1))
    
    if wait_for_service "http://localhost:$port/health" "Backend Server $server_num"; then
        echo "📚 Backend Server $server_num API Documentation: http://localhost:$port/docs"
    else
        echo "⚠️  Backend Server $server_num is not responding yet"
    fi
done

# Check frontend
if wait_for_service "http://localhost:$FRONTEND_PORT" "Frontend"; then
    echo "✅ Frontend is running at http://localhost:$FRONTEND_PORT"
else
    echo "⚠️  Frontend is not responding yet"
fi

# Check Redis services
for i in "${!REDIS_PORT_ARRAY[@]}"; do
    port="${REDIS_PORT_ARRAY[$i]}"
    server_num=$((i + 1))
    
    if docker-compose -f docker-compose.distributed.yml exec -T redis-server-$server_num redis-cli ping > /dev/null 2>&1; then
        echo "✅ Redis Server $server_num is running"
    else
        echo "⚠️  Redis Server $server_num is not responding"
    fi
done

echo ""
echo "🎉 Distributed Docker Management Tool is starting up!"
echo ""
echo "📱 Web Interface: http://localhost:$FRONTEND_PORT"
echo "🔧 Backend APIs:"
for i in "${!BACKEND_PORT_ARRAY[@]}"; do
    port="${BACKEND_PORT_ARRAY[$i]}"
    server_num=$((i + 1))
    echo "   Server $server_num: http://localhost:$port"
    echo "   Server $server_num Docs: http://localhost:$port/docs"
done
echo ""
echo "📋 Management Commands:"
echo "   View logs: docker-compose -f docker-compose.distributed.yml logs -f"
echo "   Stop all: docker-compose -f docker-compose.distributed.yml down"
echo "   Restart: docker-compose -f docker-compose.distributed.yml restart"
echo ""
echo "🔧 Server Configuration:"
echo "   1. Open the web interface at http://localhost:$FRONTEND_PORT"
echo "   2. Go to the 'Servers' page"
echo "   3. Add additional backend servers as needed"
echo "   4. Switch between servers using the server selector in the header"
echo ""

# Show container status
echo "📊 Container Status:"
docker-compose -f docker-compose.distributed.yml ps

echo ""
echo "✨ Deployment complete! The distributed architecture is now running."

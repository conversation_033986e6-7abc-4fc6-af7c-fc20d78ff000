version: '3.8'

# This compose file is for deploying only the backend service on remote servers
# Use this when you want to deploy backend instances on different hosts

services:
  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    environment:
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      - DOCKER_HOST=${DOCKER_HOST:-unix:///var/run/docker.sock}
      - DOCKER_API_VERSION=${DOCKER_API_VERSION:-auto}
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_DB=${REDIS_DB:-0}
      - SERVER_NAME=${SERVER_NAME:-Docker-Server}
      - SERVER_DESCRIPTION=${SERVER_DESCRIPTION:-Docker Management Server}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - MONITORING_INTERVAL=${MONITORING_INTERVAL:-5}
      - MAX_WEBSOCKET_CONNECTIONS=${MAX_WEBSOCKET_CONNECTIONS:-100}
      - SCHEDULER_TIMEZONE=${SCHEDULER_TIMEZONE:-UTC}
      - MAX_CONCURRENT_JOBS=${MAX_CONCURRENT_JOBS:-10}
      - TC_BINARY_PATH=${TC_BINARY_PATH:-/sbin/tc}
      - IP_BINARY_PATH=${IP_BINARY_PATH:-/sbin/ip}
      - IPTABLES_BINARY_PATH=${IPTABLES_BINARY_PATH:-/usr/sbin/iptables}
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./backend:/app
    depends_on:
      - redis
    networks:
      - docker-management-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache and Message Broker
  redis:
    image: redis:7-alpine
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis-data:/data
    networks:
      - docker-management-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server --appendonly yes

volumes:
  redis-data:

networks:
  docker-management-network:
    driver: bridge

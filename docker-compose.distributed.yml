version: '3.8'

services:
  # Frontend - Single instance that can connect to multiple backends
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend-server-1
    networks:
      - docker-management-network

  # Backend Server 1 - Primary server
  backend-server-1:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - SECRET_KEY=your-secret-key-server-1
      - DOCKER_HOST=unix:///var/run/docker.sock
      - DOCKER_API_VERSION=auto
      - REDIS_HOST=redis-server-1
      - REDIS_PORT=6379
      - REDIS_DB=0
      - SERVER_NAME=Server-1
      - SERVER_DESCRIPTION=Primary Docker Management Server
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./backend:/app
    depends_on:
      - redis-server-1
    networks:
      - docker-management-network
    restart: unless-stopped

  # Backend Server 2 - Secondary server
  backend-server-2:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8001:8000"
    environment:
      - SECRET_KEY=your-secret-key-server-2
      - DOCKER_HOST=unix:///var/run/docker.sock
      - DOCKER_API_VERSION=auto
      - REDIS_HOST=redis-server-2
      - REDIS_PORT=6379
      - REDIS_DB=0
      - SERVER_NAME=Server-2
      - SERVER_DESCRIPTION=Secondary Docker Management Server
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./backend:/app
    depends_on:
      - redis-server-2
    networks:
      - docker-management-network
    restart: unless-stopped

  # Backend Server 3 - Third server
  backend-server-3:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8002:8000"
    environment:
      - SECRET_KEY=your-secret-key-server-3
      - DOCKER_HOST=unix:///var/run/docker.sock
      - DOCKER_API_VERSION=auto
      - REDIS_HOST=redis-server-3
      - REDIS_PORT=6379
      - REDIS_DB=0
      - SERVER_NAME=Server-3
      - SERVER_DESCRIPTION=Third Docker Management Server
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./backend:/app
    depends_on:
      - redis-server-3
    networks:
      - docker-management-network
    restart: unless-stopped

  # Redis for Server 1
  redis-server-1:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-server-1-data:/data
    networks:
      - docker-management-network
    restart: unless-stopped

  # Redis for Server 2
  redis-server-2:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis-server-2-data:/data
    networks:
      - docker-management-network
    restart: unless-stopped

  # Redis for Server 3
  redis-server-3:
    image: redis:7-alpine
    ports:
      - "6381:6379"
    volumes:
      - redis-server-3-data:/data
    networks:
      - docker-management-network
    restart: unless-stopped

volumes:
  redis-server-1-data:
  redis-server-2-data:
  redis-server-3-data:

networks:
  docker-management-network:
    driver: bridge

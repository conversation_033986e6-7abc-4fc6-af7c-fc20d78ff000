# Frontend Environment Configuration

# Default API URL (can be overridden by server selector)
REACT_APP_API_URL=http://localhost:8000

# Default WebSocket URL (can be overridden by server selector)
REACT_APP_WS_URL=ws://localhost:8000

# Pre-configured servers (JSON format)
# These will be loaded as default servers in addition to the local server
REACT_APP_DEFAULT_SERVERS=[
  {
    "id": "local",
    "name": "Local Server",
    "url": "http://localhost:8000",
    "description": "Local development server",
    "isDefault": true
  },
  {
    "id": "server-1",
    "name": "Production Server 1",
    "url": "http://*************:8000",
    "description": "Production server on host *************"
  },
  {
    "id": "server-2",
    "name": "Production Server 2", 
    "url": "http://*************:8000",
    "description": "Production server on host *************"
  }
]

# Application settings
REACT_APP_NAME=Docker Management Tool
REACT_APP_VERSION=1.0.0

# Feature flags
REACT_APP_ENABLE_MULTI_SERVER=true
REACT_APP_ENABLE_REAL_TIME_MONITORING=true
REACT_APP_ENABLE_TRAFFIC_CONTROL=true

# UI settings
REACT_APP_THEME=light
REACT_APP_AUTO_REFRESH_INTERVAL=30000

{"name": "docker-management-frontend", "version": "1.0.0", "description": "React frontend for Docker Management Tool", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-charts": "^6.18.1", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "axios": "^1.6.2", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "lodash": "^4.17.21", "web-vitals": "^3.5.0"}, "devDependencies": {"@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}
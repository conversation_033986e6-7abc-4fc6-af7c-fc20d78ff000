import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box } from '@mui/material';

import { ServerProvider } from './contexts/ServerContext';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard';
import Containers from './pages/Containers';
import Networks from './pages/Networks';
import Monitoring from './pages/Monitoring';
import Scheduling from './pages/Scheduling';
import TrafficControl from './pages/TrafficControl';
import Servers from './pages/Servers';

function App() {
  return (
    <ServerProvider>
      <Box sx={{ display: 'flex' }}>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/containers" element={<Containers />} />
            <Route path="/networks" element={<Networks />} />
            <Route path="/monitoring" element={<Monitoring />} />
            <Route path="/scheduling" element={<Scheduling />} />
            <Route path="/traffic-control" element={<TrafficControl />} />
            <Route path="/servers" element={<Servers />} />
          </Routes>
        </Layout>
      </Box>
    </ServerProvider>
  );
}

export default App;

import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box } from '@mui/material';

import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard';
import Containers from './pages/Containers';
import Networks from './pages/Networks';
import Monitoring from './pages/Monitoring';
import Scheduling from './pages/Scheduling';
import TrafficControl from './pages/TrafficControl';

function App() {
  return (
    <Box sx={{ display: 'flex' }}>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/containers" element={<Containers />} />
          <Route path="/networks" element={<Networks />} />
          <Route path="/monitoring" element={<Monitoring />} />
          <Route path="/scheduling" element={<Scheduling />} />
          <Route path="/traffic-control" element={<TrafficControl />} />
        </Routes>
      </Layout>
    </Box>
  );
}

export default App;

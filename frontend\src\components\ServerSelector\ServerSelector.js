import React, { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Typography,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  CheckCircle as OnlineIcon,
  Error as OfflineIcon,
  Warning as UnknownIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

import { useServer, useServerHealth } from '../../contexts/ServerContext';
import { setCurrentServerUrl } from '../../services/api';

function ServerSelector() {
  const navigate = useNavigate();
  const {
    servers,
    currentServer,
    currentServerId,
    switchServer,
    serverStatuses,
    checkAllServersHealth,
  } = useServer();

  const { healthData, isLoading, refetch } = useServerHealth();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Update API base URL when server changes
  useEffect(() => {
    if (currentServer) {
      setCurrentServerUrl(currentServer.url);
    }
  }, [currentServer]);

  const handleServerChange = (event) => {
    const serverId = event.target.value;
    switchServer(serverId);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refetch();
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleManageServers = () => {
    navigate('/servers');
  };

  const getStatusIcon = (serverId) => {
    const status = serverStatuses[serverId] || healthData?.[serverId];
    
    if (isLoading || isRefreshing) {
      return <CircularProgress size={16} />;
    }

    switch (status?.status) {
      case 'online':
        return <OnlineIcon color="success" fontSize="small" />;
      case 'offline':
        return <OfflineIcon color="error" fontSize="small" />;
      default:
        return <UnknownIcon color="warning" fontSize="small" />;
    }
  };

  const getStatusColor = (serverId) => {
    const status = serverStatuses[serverId] || healthData?.[serverId];
    
    switch (status?.status) {
      case 'online':
        return 'success';
      case 'offline':
        return 'error';
      default:
        return 'warning';
    }
  };

  const getCurrentServerStatus = () => {
    const status = serverStatuses[currentServerId] || healthData?.[currentServerId];
    return status;
  };

  const currentStatus = getCurrentServerStatus();

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <Typography variant="body2" color="text.secondary" sx={{ minWidth: 'fit-content' }}>
        Server:
      </Typography>
      
      <FormControl size="small" sx={{ minWidth: 200 }}>
        <Select
          value={currentServerId}
          onChange={handleServerChange}
          displayEmpty
          sx={{
            '& .MuiSelect-select': {
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            },
          }}
        >
          {servers.map((server) => (
            <MenuItem key={server.id} value={server.id}>
              <Box display="flex" alignItems="center" gap={1} width="100%">
                {getStatusIcon(server.id)}
                <Box flex={1}>
                  <Typography variant="body2">{server.name}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {server.url}
                  </Typography>
                </Box>
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Current server status chip */}
      <Chip
        icon={getStatusIcon(currentServerId)}
        label={currentStatus?.status || 'unknown'}
        color={getStatusColor(currentServerId)}
        size="small"
        variant="outlined"
      />

      {/* Server info tooltip */}
      {currentStatus && (
        <Tooltip
          title={
            <Box>
              <Typography variant="body2" gutterBottom>
                <strong>{currentServer?.name}</strong>
              </Typography>
              <Typography variant="caption" display="block">
                URL: {currentServer?.url}
              </Typography>
              <Typography variant="caption" display="block">
                Status: {currentStatus.status}
              </Typography>
              {currentStatus.hostname && (
                <Typography variant="caption" display="block">
                  Hostname: {currentStatus.hostname}
                </Typography>
              )}
              {currentStatus.version && (
                <Typography variant="caption" display="block">
                  Version: {currentStatus.version}
                </Typography>
              )}
              {currentStatus.dockerVersion && (
                <Typography variant="caption" display="block">
                  Docker: {currentStatus.dockerVersion}
                </Typography>
              )}
              {currentStatus.error && (
                <Typography variant="caption" color="error" display="block">
                  Error: {currentStatus.error}
                </Typography>
              )}
            </Box>
          }
        >
          <IconButton size="small" color="inherit">
            <OnlineIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      )}

      {/* Refresh button */}
      <Tooltip title="Refresh server status">
        <IconButton
          size="small"
          onClick={handleRefresh}
          disabled={isRefreshing}
          color="inherit"
        >
          {isRefreshing ? (
            <CircularProgress size={16} />
          ) : (
            <RefreshIcon fontSize="small" />
          )}
        </IconButton>
      </Tooltip>

      {/* Manage servers button */}
      <Tooltip title="Manage servers">
        <IconButton
          size="small"
          onClick={handleManageServers}
          color="inherit"
        >
          <SettingsIcon fontSize="small" />
        </IconButton>
      </Tooltip>

      {/* Connection error alert */}
      {currentStatus?.status === 'offline' && (
        <Alert 
          severity="error" 
          sx={{ 
            position: 'absolute', 
            top: 60, 
            right: 16, 
            zIndex: 1300,
            minWidth: 300,
          }}
        >
          <Typography variant="body2">
            Cannot connect to {currentServer?.name}
          </Typography>
          <Typography variant="caption">
            {currentStatus.error}
          </Typography>
        </Alert>
      )}
    </Box>
  );
}

export default ServerSelector;

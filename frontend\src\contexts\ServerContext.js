import React, { createContext, useContext, useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import axios from 'axios';

const ServerContext = createContext();

export const useServer = () => {
  const context = useContext(ServerContext);
  if (!context) {
    throw new Error('useServer must be used within a ServerProvider');
  }
  return context;
};

// Default server configuration
const DEFAULT_SERVERS = [
  {
    id: 'local',
    name: 'Local Server',
    url: 'http://localhost:8000',
    description: 'Local development server',
    isDefault: true,
  },
];

export const ServerProvider = ({ children }) => {
  const [servers, setServers] = useState(() => {
    const saved = localStorage.getItem('docker-manager-servers');
    return saved ? JSON.parse(saved) : DEFAULT_SERVERS;
  });

  const [currentServerId, setCurrentServerId] = useState(() => {
    const saved = localStorage.getItem('docker-manager-current-server');
    return saved || 'local';
  });

  const [serverStatuses, setServerStatuses] = useState({});

  // Get current server
  const currentServer = servers.find(server => server.id === currentServerId) || servers[0];

  // Save servers to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('docker-manager-servers', JSON.stringify(servers));
  }, [servers]);

  // Save current server ID to localStorage
  useEffect(() => {
    localStorage.setItem('docker-manager-current-server', currentServerId);
  }, [currentServerId]);

  // Check server health
  const checkServerHealth = async (server) => {
    try {
      const response = await axios.get(`${server.url}/health`, {
        timeout: 5000,
      });
      
      return {
        status: 'online',
        responseTime: Date.now(),
        version: response.data.version || 'Unknown',
        dockerVersion: response.data.docker_version || 'Unknown',
        hostname: response.data.hostname || 'Unknown',
      };
    } catch (error) {
      return {
        status: 'offline',
        error: error.message,
        responseTime: null,
      };
    }
  };

  // Check all servers health
  const checkAllServersHealth = async () => {
    const statuses = {};
    
    for (const server of servers) {
      statuses[server.id] = await checkServerHealth(server);
    }
    
    setServerStatuses(statuses);
    return statuses;
  };

  // Add a new server
  const addServer = (serverConfig) => {
    const newServer = {
      id: `server-${Date.now()}`,
      ...serverConfig,
      isDefault: false,
    };
    
    setServers(prev => [...prev, newServer]);
    return newServer;
  };

  // Update an existing server
  const updateServer = (serverId, updates) => {
    setServers(prev => 
      prev.map(server => 
        server.id === serverId 
          ? { ...server, ...updates }
          : server
      )
    );
  };

  // Remove a server
  const removeServer = (serverId) => {
    // Don't allow removing the last server or default server
    if (servers.length <= 1) {
      throw new Error('Cannot remove the last server');
    }
    
    const serverToRemove = servers.find(s => s.id === serverId);
    if (serverToRemove?.isDefault) {
      throw new Error('Cannot remove the default server');
    }

    setServers(prev => prev.filter(server => server.id !== serverId));
    
    // If we're removing the current server, switch to the first available
    if (currentServerId === serverId) {
      const remainingServers = servers.filter(server => server.id !== serverId);
      setCurrentServerId(remainingServers[0]?.id);
    }
  };

  // Switch to a different server
  const switchServer = (serverId) => {
    const server = servers.find(s => s.id === serverId);
    if (server) {
      setCurrentServerId(serverId);
    }
  };

  // Get server by ID
  const getServer = (serverId) => {
    return servers.find(server => server.id === serverId);
  };

  // Get server status
  const getServerStatus = (serverId) => {
    return serverStatuses[serverId] || { status: 'unknown' };
  };

  const value = {
    servers,
    currentServer,
    currentServerId,
    serverStatuses,
    addServer,
    updateServer,
    removeServer,
    switchServer,
    getServer,
    getServerStatus,
    checkServerHealth,
    checkAllServersHealth,
  };

  return (
    <ServerContext.Provider value={value}>
      {children}
    </ServerContext.Provider>
  );
};

// Hook for server health monitoring
export const useServerHealth = () => {
  const { servers, checkAllServersHealth } = useServer();

  const { data: healthData, isLoading, error, refetch } = useQuery(
    ['server-health', servers.map(s => s.id).join(',')],
    checkAllServersHealth,
    {
      refetchInterval: 30000, // Check every 30 seconds
      retry: 1,
      staleTime: 10000, // Consider data stale after 10 seconds
    }
  );

  return {
    healthData,
    isLoading,
    error,
    refetch,
  };
};

import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Refresh as RestartIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useForm, Controller } from 'react-hook-form';
import toast from 'react-hot-toast';

import { containerAPI, networkAPI } from '../services/api';

function Containers() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedContainer, setSelectedContainer] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  const { control, handleSubmit, reset, watch } = useForm({
    defaultValues: {
      name: '',
      image: '',
      command: '',
      network: '',
      ip_address: '',
      restart_policy: 'unless-stopped',
      ports: '',
      volumes: '',
      environment: '',
    },
  });

  // Fetch containers
  const { data: containers, isLoading, error } = useQuery(
    'containers',
    () => containerAPI.list(true),
    { refetchInterval: 10000 }
  );

  // Fetch networks for container creation
  const { data: networks } = useQuery('networks', networkAPI.list);

  // Mutations
  const createMutation = useMutation(containerAPI.create, {
    onSuccess: () => {
      queryClient.invalidateQueries('containers');
      setCreateDialogOpen(false);
      reset();
      toast.success('Container created successfully');
    },
    onError: (error) => {
      toast.error(`Failed to create container: ${error.response?.data?.detail || error.message}`);
    },
  });

  const startMutation = useMutation(containerAPI.start, {
    onSuccess: () => {
      queryClient.invalidateQueries('containers');
      toast.success('Container started');
    },
    onError: (error) => {
      toast.error(`Failed to start container: ${error.response?.data?.detail || error.message}`);
    },
  });

  const stopMutation = useMutation(containerAPI.stop, {
    onSuccess: () => {
      queryClient.invalidateQueries('containers');
      toast.success('Container stopped');
    },
    onError: (error) => {
      toast.error(`Failed to stop container: ${error.response?.data?.detail || error.message}`);
    },
  });

  const restartMutation = useMutation(containerAPI.restart, {
    onSuccess: () => {
      queryClient.invalidateQueries('containers');
      toast.success('Container restarted');
    },
    onError: (error) => {
      toast.error(`Failed to restart container: ${error.response?.data?.detail || error.message}`);
    },
  });

  const deleteMutation = useMutation(
    ({ id, force }) => containerAPI.delete(id, force, false),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('containers');
        toast.success('Container deleted');
      },
      onError: (error) => {
        toast.error(`Failed to delete container: ${error.response?.data?.detail || error.message}`);
      },
    }
  );

  const handleCreateContainer = (data) => {
    const containerData = {
      name: data.name,
      image: data.image,
      command: data.command || null,
      network: data.network || null,
      ip_address: data.ip_address || null,
      restart_policy: data.restart_policy,
      ports: data.ports ? JSON.parse(data.ports) : null,
      volumes: data.volumes ? parseVolumes(data.volumes) : null,
      environment: data.environment ? parseEnvironment(data.environment) : null,
    };

    createMutation.mutate(containerData);
  };

  const parseVolumes = (volumeString) => {
    try {
      const volumes = [];
      const lines = volumeString.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        const [hostPath, containerPath, mode = 'rw'] = line.split(':');
        if (hostPath && containerPath) {
          volumes.push({
            host_path: hostPath.trim(),
            container_path: containerPath.trim(),
            mode: mode.trim(),
          });
        }
      }
      
      return volumes;
    } catch (error) {
      throw new Error('Invalid volume format. Use: host_path:container_path:mode');
    }
  };

  const parseEnvironment = (envString) => {
    try {
      const env = {};
      const lines = envString.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        const [key, ...valueParts] = line.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
      
      return env;
    } catch (error) {
      throw new Error('Invalid environment format. Use: KEY=value');
    }
  };

  const getStatusColor = (state) => {
    switch (state) {
      case 'running':
        return 'success';
      case 'exited':
        return 'error';
      case 'paused':
        return 'warning';
      default:
        return 'default';
    }
  };

  const handleViewContainer = async (container) => {
    setSelectedContainer(container);
    setViewDialogOpen(true);
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        Failed to load containers: {error.response?.data?.detail || error.message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Containers</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Container
        </Button>
      </Box>

      <Grid container spacing={3}>
        {containers?.data?.map((container) => (
          <Grid item xs={12} md={6} lg={4} key={container.id}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Typography variant="h6" noWrap>
                    {container.name}
                  </Typography>
                  <Chip
                    label={container.state}
                    color={getStatusColor(container.state)}
                    size="small"
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Image: {container.image}
                </Typography>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  ID: {container.id.substring(0, 12)}
                </Typography>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Status: {container.status}
                </Typography>

                {container.network_settings?.ip_address && (
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    IP: {container.network_settings.ip_address}
                  </Typography>
                )}

                <Box display="flex" justifyContent="space-between" mt={2}>
                  <Box>
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => handleViewContainer(container)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>

                    {container.state === 'running' ? (
                      <>
                        <Tooltip title="Stop">
                          <IconButton
                            size="small"
                            onClick={() => stopMutation.mutate(container.id)}
                            disabled={stopMutation.isLoading}
                          >
                            <StopIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Restart">
                          <IconButton
                            size="small"
                            onClick={() => restartMutation.mutate(container.id)}
                            disabled={restartMutation.isLoading}
                          >
                            <RestartIcon />
                          </IconButton>
                        </Tooltip>
                      </>
                    ) : (
                      <Tooltip title="Start">
                        <IconButton
                          size="small"
                          onClick={() => startMutation.mutate(container.id)}
                          disabled={startMutation.isLoading}
                        >
                          <StartIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>

                  <Tooltip title="Delete">
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => deleteMutation.mutate({ 
                        id: container.id, 
                        force: container.state === 'running' 
                      })}
                      disabled={deleteMutation.isLoading}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Create Container Dialog */}
      <Dialog 
        open={createDialogOpen} 
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create New Container</DialogTitle>
        <form onSubmit={handleSubmit(handleCreateContainer)}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: 'Container name is required' }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Container Name"
                      fullWidth
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="image"
                  control={control}
                  rules={{ required: 'Docker image is required' }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Docker Image"
                      fullWidth
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                      placeholder="nginx:latest"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="command"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Command (optional)"
                      fullWidth
                      placeholder="/bin/bash"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="network"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Network</InputLabel>
                      <Select {...field} label="Network">
                        <MenuItem value="">None</MenuItem>
                        {networks?.data?.map((network) => (
                          <MenuItem key={network.id} value={network.name}>
                            {network.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="ip_address"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="IP Address (optional)"
                      fullWidth
                      placeholder="*************"
                      disabled={!watch('network')}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="restart_policy"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Restart Policy</InputLabel>
                      <Select {...field} label="Restart Policy">
                        <MenuItem value="no">No</MenuItem>
                        <MenuItem value="always">Always</MenuItem>
                        <MenuItem value="unless-stopped">Unless Stopped</MenuItem>
                        <MenuItem value="on-failure">On Failure</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="ports"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Port Mappings (JSON)"
                      fullWidth
                      placeholder='{"80": 8080, "443": 8443}'
                      helperText="Format: {container_port: host_port}"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="volumes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Volume Mounts"
                      fullWidth
                      multiline
                      rows={3}
                      placeholder="/host/path:/container/path:rw"
                      helperText="One mount per line: host_path:container_path:mode"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="environment"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Environment Variables"
                      fullWidth
                      multiline
                      rows={3}
                      placeholder="KEY=value"
                      helperText="One variable per line: KEY=value"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
            <Button 
              type="submit" 
              variant="contained"
              disabled={createMutation.isLoading}
            >
              {createMutation.isLoading ? <CircularProgress size={20} /> : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* View Container Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Container Details</DialogTitle>
        <DialogContent>
          {selectedContainer && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedContainer.name}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>ID:</strong> {selectedContainer.id}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Image:</strong> {selectedContainer.image}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>State:</strong> {selectedContainer.state}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Status:</strong> {selectedContainer.status}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Created:</strong> {new Date(selectedContainer.created).toLocaleString()}
              </Typography>
              {selectedContainer.network_settings?.networks && (
                <Box>
                  <Typography variant="body2" gutterBottom>
                    <strong>Networks:</strong>
                  </Typography>
                  {Object.entries(selectedContainer.network_settings.networks).map(([name, config]) => (
                    <Typography key={name} variant="body2" sx={{ ml: 2 }}>
                      • {name}: {config.IPAddress || 'No IP'}
                    </Typography>
                  ))}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Containers;

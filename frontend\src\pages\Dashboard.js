import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  Computer as SystemIcon,
  ViewList as ContainersIcon,
  NetworkCheck as NetworksIcon,
  Warning as AlertIcon,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

import { monitoringAPI, containerAPI, networkAPI } from '../services/api';
import { createWebSocketConnection } from '../services/api';

function Dashboard() {
  const [realTimeData, setRealTimeData] = useState(null);
  const [wsConnection, setWsConnection] = useState(null);

  // Fetch initial data
  const { data: systemStats, isLoading: systemLoading } = useQuery(
    'systemStats',
    monitoringAPI.getSystem,
    { refetchInterval: 30000 }
  );

  const { data: containers, isLoading: containersLoading } = useQuery(
    'containers',
    () => containerAPI.list(true),
    { refetchInterval: 30000 }
  );

  const { data: networks, isLoading: networksLoading } = useQuery(
    'networks',
    networkAPI.list,
    { refetchInterval: 30000 }
  );

  const { data: alerts } = useQuery(
    'alerts',
    monitoringAPI.getAlerts,
    { refetchInterval: 10000 }
  );

  // WebSocket connection for real-time updates
  useEffect(() => {
    const ws = createWebSocketConnection(
      (data) => {
        if (data.type === 'monitoring_update') {
          setRealTimeData(data.data);
        }
      },
      (error) => {
        console.error('WebSocket error:', error);
      }
    );

    setWsConnection(ws);

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, []);

  const getContainerStatusCounts = () => {
    if (!containers?.data) return { running: 0, stopped: 0, total: 0 };
    
    const running = containers.data.filter(c => c.state === 'running').length;
    const total = containers.data.length;
    const stopped = total - running;
    
    return { running, stopped, total };
  };

  const getSystemMetrics = () => {
    const data = realTimeData?.system || systemStats?.data;
    if (!data) return null;

    return {
      cpu: data.cpu_percent || 0,
      memory: data.memory_percent || 0,
      disk: data.disk_percent || 0,
      uptime: data.uptime || 0,
    };
  };

  const formatUptime = (seconds) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const containerStats = getContainerStatusCounts();
  const systemMetrics = getSystemMetrics();

  if (systemLoading || containersLoading || networksLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>

      {/* Alerts */}
      {alerts?.data && alerts.data.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }} icon={<AlertIcon />}>
          <Typography variant="subtitle1" gutterBottom>
            Active Alerts ({alerts.data.length})
          </Typography>
          {alerts.data.slice(0, 3).map((alert, index) => (
            <Typography key={index} variant="body2">
              • {alert.title}: {alert.message}
            </Typography>
          ))}
        </Alert>
      )}

      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    System Status
                  </Typography>
                  <Typography variant="h5">
                    {systemMetrics ? 'Online' : 'Loading...'}
                  </Typography>
                  {systemMetrics && (
                    <Typography variant="body2" color="textSecondary">
                      Uptime: {formatUptime(systemMetrics.uptime)}
                    </Typography>
                  )}
                </Box>
                <SystemIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Containers
                  </Typography>
                  <Typography variant="h5">
                    {containerStats.total}
                  </Typography>
                  <Box display="flex" gap={1} mt={1}>
                    <Chip 
                      label={`${containerStats.running} Running`} 
                      color="success" 
                      size="small" 
                    />
                    <Chip 
                      label={`${containerStats.stopped} Stopped`} 
                      color="default" 
                      size="small" 
                    />
                  </Box>
                </Box>
                <ContainersIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Networks
                  </Typography>
                  <Typography variant="h5">
                    {networks?.data?.length || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Docker networks
                  </Typography>
                </Box>
                <NetworksIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Alerts
                  </Typography>
                  <Typography variant="h5">
                    {alerts?.data?.length || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Active alerts
                  </Typography>
                </Box>
                <AlertIcon color={alerts?.data?.length > 0 ? "warning" : "disabled"} sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Metrics */}
      {systemMetrics && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  System Resources
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="body2">CPU Usage</Typography>
                    <Typography variant="body2">{systemMetrics.cpu.toFixed(1)}%</Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={systemMetrics.cpu} 
                    color={systemMetrics.cpu > 80 ? "error" : systemMetrics.cpu > 60 ? "warning" : "primary"}
                    sx={{ mt: 1 }}
                  />
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="body2">Memory Usage</Typography>
                    <Typography variant="body2">{systemMetrics.memory.toFixed(1)}%</Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={systemMetrics.memory} 
                    color={systemMetrics.memory > 80 ? "error" : systemMetrics.memory > 60 ? "warning" : "primary"}
                    sx={{ mt: 1 }}
                  />
                </Box>

                <Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="body2">Disk Usage</Typography>
                    <Typography variant="body2">{systemMetrics.disk.toFixed(1)}%</Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={systemMetrics.disk} 
                    color={systemMetrics.disk > 80 ? "error" : systemMetrics.disk > 60 ? "warning" : "primary"}
                    sx={{ mt: 1 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Real-time Monitoring
                </Typography>
                <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
                  {realTimeData ? (
                    <Typography color="success.main">
                      ✓ Live data streaming
                    </Typography>
                  ) : (
                    <Typography color="text.secondary">
                      Connecting to real-time data...
                    </Typography>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
}

export default Dashboard;

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
} from '@mui/material';
import { useQuery } from 'react-query';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';

import { monitoringAPI, createWebSocketConnection } from '../services/api';

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`monitoring-tabpanel-${index}`}
      aria-labelledby={`monitoring-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function Monitoring() {
  const [tabValue, setTabValue] = useState(0);
  const [realTimeData, setRealTimeData] = useState(null);
  const [historicalData, setHistoricalData] = useState([]);

  // Fetch monitoring data
  const { data: dashboardData, isLoading, error } = useQuery(
    'monitoringDashboard',
    monitoringAPI.getDashboard,
    { refetchInterval: 30000 }
  );

  const { data: alerts } = useQuery(
    'monitoringAlerts',
    monitoringAPI.getAlerts,
    { refetchInterval: 10000 }
  );

  // WebSocket connection for real-time updates
  useEffect(() => {
    const ws = createWebSocketConnection(
      (data) => {
        if (data.type === 'monitoring_update') {
          setRealTimeData(data.data);
          
          // Add to historical data for charts
          const timestamp = new Date().toLocaleTimeString();
          const newDataPoint = {
            time: timestamp,
            cpu: data.data.system?.cpu_percent || 0,
            memory: data.data.system?.memory_percent || 0,
            disk: data.data.system?.disk_percent || 0,
          };
          
          setHistoricalData(prev => {
            const updated = [...prev, newDataPoint];
            // Keep only last 20 data points
            return updated.slice(-20);
          });
        }
      },
      (error) => {
        console.error('WebSocket error:', error);
      }
    );

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, []);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        Failed to load monitoring data: {error.response?.data?.detail || error.message}
      </Alert>
    );
  }

  const systemData = realTimeData?.system || dashboardData?.data?.system;
  const containerData = realTimeData?.containers || dashboardData?.data?.containers || [];
  const networkData = realTimeData?.networks || dashboardData?.data?.networks || [];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Monitoring
      </Typography>

      {/* Alerts */}
      {alerts?.data && alerts.data.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Active Alerts ({alerts.data.length})
          </Typography>
          {alerts.data.slice(0, 3).map((alert, index) => (
            <Typography key={index} variant="body2">
              • {alert.title}: {alert.message}
            </Typography>
          ))}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="System Overview" />
          <Tab label="Containers" />
          <Tab label="Networks" />
          <Tab label="Real-time Charts" />
        </Tabs>
      </Box>

      {/* System Overview Tab */}
      <TabPanel value={tabValue} index={0}>
        {systemData && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    System Resources
                  </Typography>
                  
                  <Box sx={{ mb: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2">CPU Usage</Typography>
                      <Typography variant="body2">{systemData.cpu_percent?.toFixed(1)}%</Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={systemData.cpu_percent || 0} 
                      color={systemData.cpu_percent > 80 ? "error" : systemData.cpu_percent > 60 ? "warning" : "primary"}
                      sx={{ mt: 1 }}
                    />
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2">Memory Usage</Typography>
                      <Typography variant="body2">{systemData.memory_percent?.toFixed(1)}%</Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={systemData.memory_percent || 0} 
                      color={systemData.memory_percent > 80 ? "error" : systemData.memory_percent > 60 ? "warning" : "primary"}
                      sx={{ mt: 1 }}
                    />
                  </Box>

                  <Box>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2">Disk Usage</Typography>
                      <Typography variant="body2">{systemData.disk_percent?.toFixed(1)}%</Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={systemData.disk_percent || 0} 
                      color={systemData.disk_percent > 80 ? "error" : systemData.disk_percent > 60 ? "warning" : "primary"}
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    System Information
                  </Typography>
                  
                  <Typography variant="body2" paragraph>
                    <strong>CPU Cores:</strong> {systemData.cpu_count}
                  </Typography>
                  
                  <Typography variant="body2" paragraph>
                    <strong>Total Memory:</strong> {formatBytes(systemData.memory_total)}
                  </Typography>
                  
                  <Typography variant="body2" paragraph>
                    <strong>Available Memory:</strong> {formatBytes(systemData.memory_available)}
                  </Typography>
                  
                  <Typography variant="body2" paragraph>
                    <strong>Total Disk:</strong> {formatBytes(systemData.disk_total)}
                  </Typography>
                  
                  <Typography variant="body2" paragraph>
                    <strong>Free Disk:</strong> {formatBytes(systemData.disk_free)}
                  </Typography>
                  
                  <Typography variant="body2" paragraph>
                    <strong>Uptime:</strong> {formatUptime(systemData.uptime)}
                  </Typography>
                  
                  {systemData.load_avg_1 !== undefined && (
                    <Typography variant="body2" paragraph>
                      <strong>Load Average:</strong> {systemData.load_avg_1?.toFixed(2)}, {systemData.load_avg_5?.toFixed(2)}, {systemData.load_avg_15?.toFixed(2)}
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}
      </TabPanel>

      {/* Containers Tab */}
      <TabPanel value={tabValue} index={1}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Container Statistics
            </Typography>
            
            {containerData.length > 0 ? (
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Container</TableCell>
                      <TableCell>CPU %</TableCell>
                      <TableCell>Memory %</TableCell>
                      <TableCell>Memory Usage</TableCell>
                      <TableCell>Network I/O</TableCell>
                      <TableCell>Block I/O</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {containerData.map((container) => (
                      <TableRow key={container.container_id}>
                        <TableCell>{container.container_name}</TableCell>
                        <TableCell>{container.cpu?.cpu_percent?.toFixed(1)}%</TableCell>
                        <TableCell>{container.memory?.percent?.toFixed(1)}%</TableCell>
                        <TableCell>{formatBytes(container.memory?.usage || 0)}</TableCell>
                        <TableCell>
                          ↓ {formatBytes(container.network?.rx_bytes || 0)} / 
                          ↑ {formatBytes(container.network?.tx_bytes || 0)}
                        </TableCell>
                        <TableCell>
                          R: {formatBytes(container.block_io?.read_bytes || 0)} / 
                          W: {formatBytes(container.block_io?.write_bytes || 0)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No running containers found
              </Typography>
            )}
          </CardContent>
        </Card>
      </TabPanel>

      {/* Networks Tab */}
      <TabPanel value={tabValue} index={2}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Network Interface Statistics
            </Typography>
            
            {networkData.length > 0 ? (
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Interface</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Bytes Sent</TableCell>
                      <TableCell>Bytes Received</TableCell>
                      <TableCell>Packets Sent</TableCell>
                      <TableCell>Packets Received</TableCell>
                      <TableCell>Errors</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {networkData.map((network) => (
                      <TableRow key={network.interface_name}>
                        <TableCell>{network.interface_name}</TableCell>
                        <TableCell>
                          <Typography 
                            variant="body2" 
                            color={network.is_up ? "success.main" : "error.main"}
                          >
                            {network.is_up ? "UP" : "DOWN"}
                          </Typography>
                        </TableCell>
                        <TableCell>{formatBytes(network.bytes_sent)}</TableCell>
                        <TableCell>{formatBytes(network.bytes_recv)}</TableCell>
                        <TableCell>{network.packets_sent?.toLocaleString()}</TableCell>
                        <TableCell>{network.packets_recv?.toLocaleString()}</TableCell>
                        <TableCell>{(network.errin + network.errout)?.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No network data available
              </Typography>
            )}
          </CardContent>
        </Card>
      </TabPanel>

      {/* Real-time Charts Tab */}
      <TabPanel value={tabValue} index={3}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Real-time System Metrics
                </Typography>
                
                {historicalData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={historicalData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="time" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip />
                      <Area 
                        type="monotone" 
                        dataKey="cpu" 
                        stackId="1" 
                        stroke="#8884d8" 
                        fill="#8884d8" 
                        name="CPU %"
                      />
                      <Area 
                        type="monotone" 
                        dataKey="memory" 
                        stackId="2" 
                        stroke="#82ca9d" 
                        fill="#82ca9d" 
                        name="Memory %"
                      />
                      <Area 
                        type="monotone" 
                        dataKey="disk" 
                        stackId="3" 
                        stroke="#ffc658" 
                        fill="#ffc658" 
                        name="Disk %"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <Box display="flex" justifyContent="center" alignItems="center" height={300}>
                    <Typography variant="body2" color="text.secondary">
                      Waiting for real-time data...
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Box>
  );
}

export default Monitoring;

import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Link as ConnectIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useForm, Controller } from 'react-hook-form';
import toast from 'react-hot-toast';

import { networkAPI, containerAPI } from '../services/api';

function Networks() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedNetwork, setSelectedNetwork] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [connectDialogOpen, setConnectDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  const { control, handleSubmit, reset, watch } = useForm({
    defaultValues: {
      name: '',
      driver: 'bridge',
      subnet: '',
      gateway: '',
      parent_interface: 'eth0',
      ip_range: '',
    },
  });

  const { control: connectControl, handleSubmit: handleConnectSubmit, reset: resetConnect } = useForm({
    defaultValues: {
      container_id: '',
      ip_address: '',
    },
  });

  // Fetch networks
  const { data: networks, isLoading, error } = useQuery(
    'networks',
    networkAPI.list,
    { refetchInterval: 10000 }
  );

  // Fetch containers for connection
  const { data: containers } = useQuery('containers', () => containerAPI.list(true));

  // Mutations
  const createMutation = useMutation(
    (data) => data.driver === 'macvlan' ? networkAPI.createMacvlan(data) : networkAPI.create(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('networks');
        setCreateDialogOpen(false);
        reset();
        toast.success('Network created successfully');
      },
      onError: (error) => {
        toast.error(`Failed to create network: ${error.response?.data?.detail || error.message}`);
      },
    }
  );

  const deleteMutation = useMutation(networkAPI.delete, {
    onSuccess: () => {
      queryClient.invalidateQueries('networks');
      toast.success('Network deleted');
    },
    onError: (error) => {
      toast.error(`Failed to delete network: ${error.response?.data?.detail || error.message}`);
    },
  });

  const connectMutation = useMutation(
    ({ networkId, data }) => networkAPI.connect(networkId, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('networks');
        setConnectDialogOpen(false);
        resetConnect();
        toast.success('Container connected to network');
      },
      onError: (error) => {
        toast.error(`Failed to connect container: ${error.response?.data?.detail || error.message}`);
      },
    }
  );

  const handleCreateNetwork = (data) => {
    if (data.driver === 'macvlan') {
      const networkData = {
        name: data.name,
        subnet: data.subnet,
        gateway: data.gateway,
        parent_interface: data.parent_interface,
        ip_range: data.ip_range || null,
      };
      createMutation.mutate(networkData);
    } else {
      const networkData = {
        name: data.name,
        driver: data.driver,
        ipam: data.subnet ? {
          config: [{
            subnet: data.subnet,
            gateway: data.gateway,
          }]
        } : null,
      };
      createMutation.mutate(networkData);
    }
  };

  const handleConnectContainer = (data) => {
    connectMutation.mutate({
      networkId: selectedNetwork.id,
      data: {
        container_id: data.container_id,
        ip_address: data.ip_address,
      },
    });
  };

  const handleViewNetwork = async (network) => {
    setSelectedNetwork(network);
    setViewDialogOpen(true);
  };

  const handleConnectToNetwork = (network) => {
    setSelectedNetwork(network);
    setConnectDialogOpen(true);
  };

  const getDriverColor = (driver) => {
    switch (driver) {
      case 'bridge':
        return 'primary';
      case 'macvlan':
        return 'secondary';
      case 'overlay':
        return 'success';
      case 'host':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        Failed to load networks: {error.response?.data?.detail || error.message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Networks</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Network
        </Button>
      </Box>

      <Grid container spacing={3}>
        {networks?.data?.map((network) => (
          <Grid item xs={12} md={6} lg={4} key={network.id}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Typography variant="h6" noWrap>
                    {network.name}
                  </Typography>
                  <Chip
                    label={network.driver}
                    color={getDriverColor(network.driver)}
                    size="small"
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  ID: {network.id.substring(0, 12)}
                </Typography>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Scope: {network.scope}
                </Typography>

                {network.ipam?.config?.[0] && (
                  <>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Subnet: {network.ipam.config[0].subnet}
                    </Typography>
                    {network.ipam.config[0].gateway && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Gateway: {network.ipam.config[0].gateway}
                      </Typography>
                    )}
                  </>
                )}

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Containers: {network.containers?.length || 0}
                </Typography>

                <Box display="flex" justifyContent="space-between" mt={2}>
                  <Box>
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => handleViewNetwork(network)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="Connect Container">
                      <IconButton
                        size="small"
                        onClick={() => handleConnectToNetwork(network)}
                      >
                        <ConnectIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>

                  {!['bridge', 'host', 'none'].includes(network.name) && (
                    <Tooltip title="Delete">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => deleteMutation.mutate(network.id)}
                        disabled={deleteMutation.isLoading}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Create Network Dialog */}
      <Dialog 
        open={createDialogOpen} 
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create New Network</DialogTitle>
        <form onSubmit={handleSubmit(handleCreateNetwork)}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: 'Network name is required' }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Network Name"
                      fullWidth
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="driver"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Driver</InputLabel>
                      <Select {...field} label="Driver">
                        <MenuItem value="bridge">Bridge</MenuItem>
                        <MenuItem value="macvlan">Macvlan</MenuItem>
                        <MenuItem value="overlay">Overlay</MenuItem>
                        <MenuItem value="host">Host</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="subnet"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Subnet (CIDR)"
                      fullWidth
                      placeholder="***********/24"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="gateway"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Gateway"
                      fullWidth
                      placeholder="***********"
                    />
                  )}
                />
              </Grid>

              {watch('driver') === 'macvlan' && (
                <>
                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="parent_interface"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Parent Interface"
                          fullWidth
                          placeholder="eth0"
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="ip_range"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="IP Range (optional)"
                          fullWidth
                          placeholder="***********00/28"
                        />
                      )}
                    />
                  </Grid>
                </>
              )}
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
            <Button 
              type="submit" 
              variant="contained"
              disabled={createMutation.isLoading}
            >
              {createMutation.isLoading ? <CircularProgress size={20} /> : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* View Network Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Network Details</DialogTitle>
        <DialogContent>
          {selectedNetwork && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedNetwork.name}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>ID:</strong> {selectedNetwork.id}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Driver:</strong> {selectedNetwork.driver}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Scope:</strong> {selectedNetwork.scope}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Created:</strong> {new Date(selectedNetwork.created).toLocaleString()}
              </Typography>

              {selectedNetwork.containers && selectedNetwork.containers.length > 0 && (
                <Box mt={2}>
                  <Typography variant="h6" gutterBottom>
                    Connected Containers
                  </Typography>
                  <TableContainer component={Paper}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Container</TableCell>
                          <TableCell>IP Address</TableCell>
                          <TableCell>MAC Address</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedNetwork.containers.map((container) => (
                          <TableRow key={container.container_id}>
                            <TableCell>{container.container_name}</TableCell>
                            <TableCell>{container.ipv4_address || 'N/A'}</TableCell>
                            <TableCell>{container.mac_address || 'N/A'}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Connect Container Dialog */}
      <Dialog
        open={connectDialogOpen}
        onClose={() => setConnectDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Connect Container to Network</DialogTitle>
        <form onSubmit={handleConnectSubmit(handleConnectContainer)}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Controller
                  name="container_id"
                  control={connectControl}
                  rules={{ required: 'Container is required' }}
                  render={({ field, fieldState }) => (
                    <FormControl fullWidth error={!!fieldState.error}>
                      <InputLabel>Container</InputLabel>
                      <Select {...field} label="Container">
                        {containers?.data?.map((container) => (
                          <MenuItem key={container.id} value={container.id}>
                            {container.name} ({container.state})
                          </MenuItem>
                        ))}
                      </Select>
                      {fieldState.error && (
                        <Typography variant="caption" color="error">
                          {fieldState.error.message}
                        </Typography>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="ip_address"
                  control={connectControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="IP Address (optional)"
                      fullWidth
                      placeholder="***********00"
                      helperText="Leave empty for automatic assignment"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setConnectDialogOpen(false)}>Cancel</Button>
            <Button 
              type="submit" 
              variant="contained"
              disabled={connectMutation.isLoading}
            >
              {connectMutation.isLoading ? <CircularProgress size={20} /> : 'Connect'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
}

export default Networks;

import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  PlayArrow as ExecuteIcon,
  Pause as DisableIcon,
  PlayCircle as EnableIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useForm, Controller } from 'react-hook-form';
import toast from 'react-hot-toast';

import { schedulingAPI, containerAPI } from '../services/api';

function Scheduling() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [scheduleType, setScheduleType] = useState('container');
  const queryClient = useQueryClient();

  const { control, handleSubmit, reset, watch } = useForm({
    defaultValues: {
      name: '',
      container_id: '',
      action: 'start',
      script_path: '',
      script_args: '',
      cron_expression: '0 9 * * *',
      timezone: 'UTC',
      enabled: true,
      description: '',
      working_directory: '',
      environment: '',
    },
  });

  // Fetch schedules
  const { data: schedules, isLoading, error } = useQuery(
    'schedules',
    schedulingAPI.list,
    { refetchInterval: 30000 }
  );

  // Fetch containers for container schedules
  const { data: containers } = useQuery('containers', () => containerAPI.list(true));

  // Fetch recent executions
  const { data: recentExecutions } = useQuery(
    'recentExecutions',
    () => schedulingAPI.getRecentExecutions(20),
    { refetchInterval: 10000 }
  );

  // Mutations
  const createMutation = useMutation(
    (data) => scheduleType === 'container' 
      ? schedulingAPI.createContainer(data) 
      : schedulingAPI.createScript(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('schedules');
        setCreateDialogOpen(false);
        reset();
        toast.success('Schedule created successfully');
      },
      onError: (error) => {
        toast.error(`Failed to create schedule: ${error.response?.data?.detail || error.message}`);
      },
    }
  );

  const deleteMutation = useMutation(schedulingAPI.delete, {
    onSuccess: () => {
      queryClient.invalidateQueries('schedules');
      toast.success('Schedule deleted');
    },
    onError: (error) => {
      toast.error(`Failed to delete schedule: ${error.response?.data?.detail || error.message}`);
    },
  });

  const enableMutation = useMutation(schedulingAPI.enable, {
    onSuccess: () => {
      queryClient.invalidateQueries('schedules');
      toast.success('Schedule enabled');
    },
    onError: (error) => {
      toast.error(`Failed to enable schedule: ${error.response?.data?.detail || error.message}`);
    },
  });

  const disableMutation = useMutation(schedulingAPI.disable, {
    onSuccess: () => {
      queryClient.invalidateQueries('schedules');
      toast.success('Schedule disabled');
    },
    onError: (error) => {
      toast.error(`Failed to disable schedule: ${error.response?.data?.detail || error.message}`);
    },
  });

  const executeMutation = useMutation(schedulingAPI.executeNow, {
    onSuccess: () => {
      queryClient.invalidateQueries(['schedules', 'recentExecutions']);
      toast.success('Schedule executed');
    },
    onError: (error) => {
      toast.error(`Failed to execute schedule: ${error.response?.data?.detail || error.message}`);
    },
  });

  const handleCreateSchedule = (data) => {
    const scheduleData = {
      name: data.name,
      cron_expression: data.cron_expression,
      timezone: data.timezone,
      enabled: data.enabled,
      description: data.description,
    };

    if (scheduleType === 'container') {
      scheduleData.container_id = data.container_id;
      scheduleData.action = data.action;
      if (data.script_path) {
        scheduleData.script_path = data.script_path;
        scheduleData.script_args = data.script_args ? data.script_args.split(' ') : [];
      }
    } else {
      scheduleData.script_path = data.script_path;
      scheduleData.script_args = data.script_args ? data.script_args.split(' ') : [];
      scheduleData.working_directory = data.working_directory || null;
      scheduleData.environment = data.environment ? parseEnvironment(data.environment) : null;
    }

    createMutation.mutate(scheduleData);
  };

  const parseEnvironment = (envString) => {
    try {
      const env = {};
      const lines = envString.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        const [key, ...valueParts] = line.split('=');
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join('=').trim();
        }
      }
      
      return env;
    } catch (error) {
      throw new Error('Invalid environment format. Use: KEY=value');
    }
  };

  const handleViewSchedule = (schedule) => {
    setSelectedSchedule(schedule);
    setViewDialogOpen(true);
  };

  const getStatusColor = (enabled) => {
    return enabled ? 'success' : 'default';
  };

  const getExecutionStatusColor = (status) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'failed':
        return 'error';
      case 'running':
        return 'info';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        Failed to load schedules: {error.response?.data?.detail || error.message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Scheduling</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Schedule
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Schedules */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Scheduled Jobs
              </Typography>
              
              {schedules?.data?.length > 0 ? (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Cron Expression</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Next Run</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {schedules.data.map((schedule) => (
                        <TableRow key={schedule.id}>
                          <TableCell>{schedule.name}</TableCell>
                          <TableCell>
                            <Chip 
                              label={schedule.schedule_type.replace('_', ' ')} 
                              size="small" 
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <code>{schedule.cron_expression}</code>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={schedule.enabled ? 'Enabled' : 'Disabled'}
                              color={getStatusColor(schedule.enabled)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {schedule.next_run 
                              ? new Date(schedule.next_run).toLocaleString()
                              : 'N/A'
                            }
                          </TableCell>
                          <TableCell>
                            <Tooltip title="View Details">
                              <IconButton
                                size="small"
                                onClick={() => handleViewSchedule(schedule)}
                              >
                                <ViewIcon />
                              </IconButton>
                            </Tooltip>

                            <Tooltip title="Execute Now">
                              <IconButton
                                size="small"
                                onClick={() => executeMutation.mutate(schedule.id)}
                                disabled={executeMutation.isLoading}
                              >
                                <ExecuteIcon />
                              </IconButton>
                            </Tooltip>

                            {schedule.enabled ? (
                              <Tooltip title="Disable">
                                <IconButton
                                  size="small"
                                  onClick={() => disableMutation.mutate(schedule.id)}
                                  disabled={disableMutation.isLoading}
                                >
                                  <DisableIcon />
                                </IconButton>
                              </Tooltip>
                            ) : (
                              <Tooltip title="Enable">
                                <IconButton
                                  size="small"
                                  onClick={() => enableMutation.mutate(schedule.id)}
                                  disabled={enableMutation.isLoading}
                                >
                                  <EnableIcon />
                                </IconButton>
                              </Tooltip>
                            )}

                            <Tooltip title="Delete">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => deleteMutation.mutate(schedule.id)}
                                disabled={deleteMutation.isLoading}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No schedules found
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Executions */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Executions
              </Typography>
              
              {recentExecutions?.data?.length > 0 ? (
                <Box>
                  {recentExecutions.data.slice(0, 10).map((execution) => (
                    <Box key={execution.id} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2" fontWeight="bold">
                          {execution.schedule_name}
                        </Typography>
                        <Chip
                          label={execution.status}
                          color={getExecutionStatusColor(execution.status)}
                          size="small"
                        />
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        Started: {new Date(execution.started_at).toLocaleString()}
                      </Typography>
                      {execution.duration && (
                        <Typography variant="caption" color="text.secondary" display="block">
                          Duration: {execution.duration.toFixed(2)}s
                        </Typography>
                      )}
                      {execution.error && (
                        <Typography variant="caption" color="error" display="block">
                          Error: {execution.error}
                        </Typography>
                      )}
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No recent executions
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Create Schedule Dialog */}
      <Dialog 
        open={createDialogOpen} 
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create New Schedule</DialogTitle>
        <form onSubmit={handleSubmit(handleCreateSchedule)}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Schedule Type</InputLabel>
                  <Select
                    value={scheduleType}
                    onChange={(e) => setScheduleType(e.target.value)}
                    label="Schedule Type"
                  >
                    <MenuItem value="container">Container Action</MenuItem>
                    <MenuItem value="script">Script Execution</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: 'Schedule name is required' }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Schedule Name"
                      fullWidth
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="cron_expression"
                  control={control}
                  rules={{ required: 'Cron expression is required' }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Cron Expression"
                      fullWidth
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message || "e.g., '0 9 * * *' for daily at 9 AM"}
                    />
                  )}
                />
              </Grid>

              {scheduleType === 'container' ? (
                <>
                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="container_id"
                      control={control}
                      rules={{ required: 'Container is required' }}
                      render={({ field, fieldState }) => (
                        <FormControl fullWidth error={!!fieldState.error}>
                          <InputLabel>Container</InputLabel>
                          <Select {...field} label="Container">
                            {containers?.data?.map((container) => (
                              <MenuItem key={container.id} value={container.id}>
                                {container.name}
                              </MenuItem>
                            ))}
                          </Select>
                          {fieldState.error && (
                            <Typography variant="caption" color="error">
                              {fieldState.error.message}
                            </Typography>
                          )}
                        </FormControl>
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="action"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth>
                          <InputLabel>Action</InputLabel>
                          <Select {...field} label="Action">
                            <MenuItem value="start">Start</MenuItem>
                            <MenuItem value="stop">Stop</MenuItem>
                            <MenuItem value="restart">Restart</MenuItem>
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>
                </>
              ) : (
                <Grid item xs={12}>
                  <Controller
                    name="script_path"
                    control={control}
                    rules={{ required: 'Script path is required' }}
                    render={({ field, fieldState }) => (
                      <TextField
                        {...field}
                        label="Script Path"
                        fullWidth
                        error={!!fieldState.error}
                        helperText={fieldState.error?.message}
                        placeholder="/path/to/script.sh"
                      />
                    )}
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <Controller
                  name="script_args"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Script Arguments (optional)"
                      fullWidth
                      placeholder="arg1 arg2 arg3"
                      helperText="Space-separated arguments"
                    />
                  )}
                />
              </Grid>

              {scheduleType === 'script' && (
                <>
                  <Grid item xs={12}>
                    <Controller
                      name="working_directory"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Working Directory (optional)"
                          fullWidth
                          placeholder="/path/to/working/dir"
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Controller
                      name="environment"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Environment Variables (optional)"
                          fullWidth
                          multiline
                          rows={3}
                          placeholder="KEY=value"
                          helperText="One variable per line: KEY=value"
                        />
                      )}
                    />
                  </Grid>
                </>
              )}

              <Grid item xs={12}>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Description (optional)"
                      fullWidth
                      multiline
                      rows={2}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="enabled"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Switch {...field} checked={field.value} />}
                      label="Enable schedule"
                    />
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
            <Button 
              type="submit" 
              variant="contained"
              disabled={createMutation.isLoading}
            >
              {createMutation.isLoading ? <CircularProgress size={20} /> : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* View Schedule Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Schedule Details</DialogTitle>
        <DialogContent>
          {selectedSchedule && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedSchedule.name}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Type:</strong> {selectedSchedule.schedule_type.replace('_', ' ')}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Cron Expression:</strong> <code>{selectedSchedule.cron_expression}</code>
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Timezone:</strong> {selectedSchedule.timezone}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Status:</strong> {selectedSchedule.enabled ? 'Enabled' : 'Disabled'}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Created:</strong> {new Date(selectedSchedule.created_at).toLocaleString()}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Last Run:</strong> {selectedSchedule.last_run ? new Date(selectedSchedule.last_run).toLocaleString() : 'Never'}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Next Run:</strong> {selectedSchedule.next_run ? new Date(selectedSchedule.next_run).toLocaleString() : 'N/A'}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Run Count:</strong> {selectedSchedule.run_count} (Success: {selectedSchedule.success_count}, Failed: {selectedSchedule.failure_count})
              </Typography>
              {selectedSchedule.description && (
                <Typography variant="body2" paragraph>
                  <strong>Description:</strong> {selectedSchedule.description}
                </Typography>
              )}
              {selectedSchedule.config && (
                <Box>
                  <Typography variant="body2" gutterBottom>
                    <strong>Configuration:</strong>
                  </Typography>
                  <pre style={{ fontSize: '0.875rem', background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                    {JSON.stringify(selectedSchedule.config, null, 2)}
                  </pre>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Scheduling;

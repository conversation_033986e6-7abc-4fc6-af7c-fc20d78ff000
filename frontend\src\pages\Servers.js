import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  CheckCircle as OnlineIcon,
  Error as OfflineIcon,
  Warning as UnknownIcon,
  Launch as LaunchIcon,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import toast from 'react-hot-toast';

import { useServer, useServerHealth } from '../contexts/ServerContext';

function Servers() {
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedServer, setSelectedServer] = useState(null);
  const [testingConnection, setTestingConnection] = useState({});

  const {
    servers,
    currentServerId,
    addServer,
    updateServer,
    removeServer,
    switchServer,
    checkServerHealth,
  } = useServer();

  const { healthData, isLoading, refetch } = useServerHealth();

  const { control, handleSubmit, reset, watch } = useForm({
    defaultValues: {
      name: '',
      url: '',
      description: '',
    },
  });

  const { control: editControl, handleSubmit: handleEditSubmit, reset: resetEdit, setValue } = useForm({
    defaultValues: {
      name: '',
      url: '',
      description: '',
    },
  });

  const handleAddServer = async (data) => {
    try {
      // Test connection first
      setTestingConnection({ add: true });
      
      const testResult = await checkServerHealth({ url: data.url });
      
      if (testResult.status === 'offline') {
        toast.error(`Cannot connect to server: ${testResult.error}`);
        return;
      }

      const newServer = addServer(data);
      setAddDialogOpen(false);
      reset();
      toast.success(`Server "${newServer.name}" added successfully`);
      
      // Refresh health data
      refetch();
    } catch (error) {
      toast.error(`Failed to add server: ${error.message}`);
    } finally {
      setTestingConnection({});
    }
  };

  const handleEditServer = async (data) => {
    try {
      if (!selectedServer) return;

      // Test connection if URL changed
      if (data.url !== selectedServer.url) {
        setTestingConnection({ edit: true });
        
        const testResult = await checkServerHealth({ url: data.url });
        
        if (testResult.status === 'offline') {
          toast.error(`Cannot connect to server: ${testResult.error}`);
          return;
        }
      }

      updateServer(selectedServer.id, data);
      setEditDialogOpen(false);
      resetEdit();
      setSelectedServer(null);
      toast.success(`Server "${data.name}" updated successfully`);
      
      // Refresh health data
      refetch();
    } catch (error) {
      toast.error(`Failed to update server: ${error.message}`);
    } finally {
      setTestingConnection({});
    }
  };

  const handleDeleteServer = (serverId) => {
    try {
      const server = servers.find(s => s.id === serverId);
      removeServer(serverId);
      toast.success(`Server "${server?.name}" removed successfully`);
    } catch (error) {
      toast.error(`Failed to remove server: ${error.message}`);
    }
  };

  const handleEditClick = (server) => {
    setSelectedServer(server);
    setValue('name', server.name);
    setValue('url', server.url);
    setValue('description', server.description || '');
    setEditDialogOpen(true);
  };

  const handleTestConnection = async (serverId) => {
    const server = servers.find(s => s.id === serverId);
    if (!server) return;

    setTestingConnection({ [serverId]: true });
    
    try {
      const result = await checkServerHealth(server);
      
      if (result.status === 'online') {
        toast.success(`Connection to "${server.name}" successful`);
      } else {
        toast.error(`Connection to "${server.name}" failed: ${result.error}`);
      }
      
      // Refresh health data
      refetch();
    } catch (error) {
      toast.error(`Failed to test connection: ${error.message}`);
    } finally {
      setTestingConnection({});
    }
  };

  const getStatusIcon = (serverId) => {
    const status = healthData?.[serverId];
    
    if (testingConnection[serverId] || isLoading) {
      return <CircularProgress size={20} />;
    }

    switch (status?.status) {
      case 'online':
        return <OnlineIcon color="success" />;
      case 'offline':
        return <OfflineIcon color="error" />;
      default:
        return <UnknownIcon color="warning" />;
    }
  };

  const getStatusColor = (serverId) => {
    const status = healthData?.[serverId];
    
    switch (status?.status) {
      case 'online':
        return 'success';
      case 'offline':
        return 'error';
      default:
        return 'warning';
    }
  };

  const formatUrl = (url) => {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.host}`;
    } catch {
      return url;
    }
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Server Management</Typography>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => refetch()}
            disabled={isLoading}
          >
            Refresh Status
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setAddDialogOpen(true)}
          >
            Add Server
          </Button>
        </Box>
      </Box>

      {/* Server List */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Backend Servers
          </Typography>
          
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>URL</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Info</TableCell>
                  <TableCell>Current</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {servers.map((server) => {
                  const status = healthData?.[server.id];
                  const isCurrent = server.id === currentServerId;
                  
                  return (
                    <TableRow key={server.id} selected={isCurrent}>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight={isCurrent ? 'bold' : 'normal'}>
                            {server.name}
                          </Typography>
                          {server.description && (
                            <Typography variant="caption" color="text.secondary">
                              {server.description}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {formatUrl(server.url)}
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          {getStatusIcon(server.id)}
                          <Chip
                            label={status?.status || 'unknown'}
                            color={getStatusColor(server.id)}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        {status?.status === 'online' && (
                          <Box>
                            {status.hostname && (
                              <Typography variant="caption" display="block">
                                Host: {status.hostname}
                              </Typography>
                            )}
                            {status.version && (
                              <Typography variant="caption" display="block">
                                API: {status.version}
                              </Typography>
                            )}
                            {status.dockerVersion && (
                              <Typography variant="caption" display="block">
                                Docker: {status.dockerVersion}
                              </Typography>
                            )}
                          </Box>
                        )}
                        {status?.error && (
                          <Typography variant="caption" color="error">
                            {status.error}
                          </Typography>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={isCurrent}
                              onChange={() => switchServer(server.id)}
                              size="small"
                            />
                          }
                          label={isCurrent ? "Active" : ""}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Box display="flex" gap={0.5}>
                          <Tooltip title="Test Connection">
                            <IconButton
                              size="small"
                              onClick={() => handleTestConnection(server.id)}
                              disabled={testingConnection[server.id]}
                            >
                              <RefreshIcon />
                            </IconButton>
                          </Tooltip>
                          
                          <Tooltip title="Open in Browser">
                            <IconButton
                              size="small"
                              onClick={() => window.open(server.url + '/docs', '_blank')}
                            >
                              <LaunchIcon />
                            </IconButton>
                          </Tooltip>
                          
                          <Tooltip title="Edit">
                            <IconButton
                              size="small"
                              onClick={() => handleEditClick(server)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          
                          {!server.isDefault && (
                            <Tooltip title="Delete">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteServer(server.id)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Add Server Dialog */}
      <Dialog 
        open={addDialogOpen} 
        onClose={() => setAddDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Server</DialogTitle>
        <form onSubmit={handleSubmit(handleAddServer)}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: 'Server name is required' }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Server Name"
                      fullWidth
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="url"
                  control={control}
                  rules={{ 
                    required: 'Server URL is required',
                    pattern: {
                      value: /^https?:\/\/.+/,
                      message: 'URL must start with http:// or https://'
                    }
                  }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Server URL"
                      fullWidth
                      placeholder="http://*************:8000"
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Description (optional)"
                      fullWidth
                      multiline
                      rows={2}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setAddDialogOpen(false)}>Cancel</Button>
            <Button 
              type="submit" 
              variant="contained"
              disabled={testingConnection.add}
            >
              {testingConnection.add ? <CircularProgress size={20} /> : 'Add Server'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Edit Server Dialog */}
      <Dialog 
        open={editDialogOpen} 
        onClose={() => setEditDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Edit Server</DialogTitle>
        <form onSubmit={handleEditSubmit(handleEditServer)}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Controller
                  name="name"
                  control={editControl}
                  rules={{ required: 'Server name is required' }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Server Name"
                      fullWidth
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="url"
                  control={editControl}
                  rules={{ 
                    required: 'Server URL is required',
                    pattern: {
                      value: /^https?:\/\/.+/,
                      message: 'URL must start with http:// or https://'
                    }
                  }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label="Server URL"
                      fullWidth
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="description"
                  control={editControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Description (optional)"
                      fullWidth
                      multiline
                      rows={2}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
            <Button 
              type="submit" 
              variant="contained"
              disabled={testingConnection.edit}
            >
              {testingConnection.edit ? <CircularProgress size={20} /> : 'Update Server'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
}

export default Servers;

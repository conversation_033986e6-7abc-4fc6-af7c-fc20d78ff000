import React, { useState } from 'react';
import {
  Box,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  PlayArrow as ApplyIcon,
  Stop as RemoveIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  Code as CodeIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useForm, Controller } from 'react-hook-form';
import toast from 'react-hot-toast';

import { trafficAPI, containerAPI } from '../services/api';

function TrafficControl() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedRule, setSelectedRule] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [ruleType, setRuleType] = useState('bandwidth');
  const queryClient = useQueryClient();

  const { control, handleSubmit, reset, watch } = useForm({
    defaultValues: {
      container_id: '',
      ingress_rate: '',
      egress_rate: '',
      burst: '',
      rule_type: 'delay',
      delay: '100ms',
      jitter: '',
      loss_percent: 1,
      duplicate_percent: 1,
      corrupt_percent: 1,
      correlation: '',
      description: '',
    },
  });

  // Fetch traffic rules
  const { data: rules, isLoading, error } = useQuery(
    'trafficRules',
    trafficAPI.listRules,
    { refetchInterval: 10000 }
  );

  // Fetch containers
  const { data: containers } = useQuery('containers', () => containerAPI.list(true));

  // Fetch system status
  const { data: systemStatus } = useQuery(
    'trafficSystemStatus',
    trafficAPI.getStatus,
    { refetchInterval: 30000 }
  );

  // Mutations
  const createBandwidthMutation = useMutation(trafficAPI.createBandwidthLimit, {
    onSuccess: () => {
      queryClient.invalidateQueries('trafficRules');
      setCreateDialogOpen(false);
      reset();
      toast.success('Bandwidth limit created successfully');
    },
    onError: (error) => {
      toast.error(`Failed to create bandwidth limit: ${error.response?.data?.detail || error.message}`);
    },
  });

  const createShapingMutation = useMutation(trafficAPI.createShapingRule, {
    onSuccess: () => {
      queryClient.invalidateQueries('trafficRules');
      setCreateDialogOpen(false);
      reset();
      toast.success('Traffic shaping rule created successfully');
    },
    onError: (error) => {
      toast.error(`Failed to create shaping rule: ${error.response?.data?.detail || error.message}`);
    },
  });

  const deleteMutation = useMutation(trafficAPI.deleteRule, {
    onSuccess: () => {
      queryClient.invalidateQueries('trafficRules');
      toast.success('Rule deleted');
    },
    onError: (error) => {
      toast.error(`Failed to delete rule: ${error.response?.data?.detail || error.message}`);
    },
  });

  const applyMutation = useMutation(trafficAPI.applyRule, {
    onSuccess: () => {
      queryClient.invalidateQueries('trafficRules');
      toast.success('Rule applied');
    },
    onError: (error) => {
      toast.error(`Failed to apply rule: ${error.response?.data?.detail || error.message}`);
    },
  });

  const removeMutation = useMutation(trafficAPI.removeRule, {
    onSuccess: () => {
      queryClient.invalidateQueries('trafficRules');
      toast.success('Rule removed');
    },
    onError: (error) => {
      toast.error(`Failed to remove rule: ${error.response?.data?.detail || error.message}`);
    },
  });

  const handleCreateRule = (data) => {
    if (ruleType === 'bandwidth') {
      const ruleData = {
        container_id: data.container_id,
        ingress_rate: data.ingress_rate || null,
        egress_rate: data.egress_rate || null,
        burst: data.burst || null,
        description: data.description || null,
      };
      createBandwidthMutation.mutate(ruleData);
    } else {
      const parameters = {};
      
      switch (data.rule_type) {
        case 'delay':
          parameters.delay = data.delay;
          if (data.jitter) parameters.jitter = data.jitter;
          if (data.correlation) parameters.correlation = parseFloat(data.correlation);
          break;
        case 'loss':
          parameters.loss_percent = data.loss_percent;
          if (data.correlation) parameters.correlation = parseFloat(data.correlation);
          break;
        case 'duplicate':
          parameters.duplicate_percent = data.duplicate_percent;
          if (data.correlation) parameters.correlation = parseFloat(data.correlation);
          break;
        case 'corrupt':
          parameters.corrupt_percent = data.corrupt_percent;
          if (data.correlation) parameters.correlation = parseFloat(data.correlation);
          break;
      }

      const ruleData = {
        container_id: data.container_id,
        rule_type: data.rule_type,
        parameters,
        description: data.description || null,
      };
      createShapingMutation.mutate(ruleData);
    }
  };

  const handleViewRule = (rule) => {
    setSelectedRule(rule);
    setViewDialogOpen(true);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'default';
      case 'error':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getRuleTypeColor = (ruleType) => {
    switch (ruleType) {
      case 'bandwidth_limit':
        return 'primary';
      case 'delay':
        return 'secondary';
      case 'packet_loss':
        return 'warning';
      case 'packet_duplicate':
        return 'info';
      case 'packet_corrupt':
        return 'error';
      default:
        return 'default';
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        Failed to load traffic rules: {error.response?.data?.detail || error.message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Traffic Control</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Rule
        </Button>
      </Box>

      {/* System Status */}
      {systemStatus?.data && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              System Status
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2">
                  <strong>TC Available:</strong> {systemStatus.data.tc_available ? '✓' : '✗'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2">
                  <strong>IP Available:</strong> {systemStatus.data.ip_available ? '✓' : '✗'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2">
                  <strong>Active Rules:</strong> {systemStatus.data.active_rules}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2">
                  <strong>Total Rules:</strong> {systemStatus.data.total_rules}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Traffic Rules */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Traffic Control Rules
          </Typography>
          
          {rules?.data?.length > 0 ? (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Container</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Parameters</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {rules.data.map((rule) => (
                    <TableRow key={rule.id}>
                      <TableCell>{rule.name}</TableCell>
                      <TableCell>{rule.container_name || rule.container_id.substring(0, 12)}</TableCell>
                      <TableCell>
                        <Chip
                          label={rule.rule_type.replace('_', ' ')}
                          color={getRuleTypeColor(rule.rule_type)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={rule.status}
                          color={getStatusColor(rule.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" component="div">
                          {Object.entries(rule.parameters).map(([key, value]) => (
                            <div key={key}>
                              <strong>{key}:</strong> {value}
                            </div>
                          ))}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleViewRule(rule)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>

                        {rule.status === 'inactive' ? (
                          <Tooltip title="Apply Rule">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => applyMutation.mutate(rule.id)}
                              disabled={applyMutation.isLoading}
                            >
                              <ApplyIcon />
                            </IconButton>
                          </Tooltip>
                        ) : (
                          <Tooltip title="Remove Rule">
                            <IconButton
                              size="small"
                              color="warning"
                              onClick={() => removeMutation.mutate(rule.id)}
                              disabled={removeMutation.isLoading}
                            >
                              <RemoveIcon />
                            </IconButton>
                          </Tooltip>
                        )}

                        <Tooltip title="Delete">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => deleteMutation.mutate(rule.id)}
                            disabled={deleteMutation.isLoading}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No traffic control rules found
            </Typography>
          )}
        </CardContent>
      </Card>

      {/* Create Rule Dialog */}
      <Dialog 
        open={createDialogOpen} 
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create Traffic Control Rule</DialogTitle>
        <form onSubmit={handleSubmit(handleCreateRule)}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Rule Type</InputLabel>
                  <Select
                    value={ruleType}
                    onChange={(e) => setRuleType(e.target.value)}
                    label="Rule Type"
                  >
                    <MenuItem value="bandwidth">Bandwidth Limiting</MenuItem>
                    <MenuItem value="shaping">Traffic Shaping</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="container_id"
                  control={control}
                  rules={{ required: 'Container is required' }}
                  render={({ field, fieldState }) => (
                    <FormControl fullWidth error={!!fieldState.error}>
                      <InputLabel>Container</InputLabel>
                      <Select {...field} label="Container">
                        {containers?.data?.map((container) => (
                          <MenuItem key={container.id} value={container.id}>
                            {container.name} ({container.state})
                          </MenuItem>
                        ))}
                      </Select>
                      {fieldState.error && (
                        <Typography variant="caption" color="error">
                          {fieldState.error.message}
                        </Typography>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              {ruleType === 'bandwidth' ? (
                <>
                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="ingress_rate"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Ingress Rate"
                          fullWidth
                          placeholder="100mbit"
                          helperText="e.g., 100mbit, 1gbit"
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="egress_rate"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Egress Rate"
                          fullWidth
                          placeholder="50mbit"
                          helperText="e.g., 50mbit, 500mbit"
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Controller
                      name="burst"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Burst Size (optional)"
                          fullWidth
                          placeholder="10mb"
                          helperText="e.g., 10mb, 1gb"
                        />
                      )}
                    />
                  </Grid>
                </>
              ) : (
                <>
                  <Grid item xs={12}>
                    <Controller
                      name="rule_type"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth>
                          <InputLabel>Shaping Type</InputLabel>
                          <Select {...field} label="Shaping Type">
                            <MenuItem value="delay">Delay</MenuItem>
                            <MenuItem value="loss">Packet Loss</MenuItem>
                            <MenuItem value="duplicate">Packet Duplication</MenuItem>
                            <MenuItem value="corrupt">Packet Corruption</MenuItem>
                          </Select>
                        </FormControl>
                      )}
                    />
                  </Grid>

                  {watch('rule_type') === 'delay' && (
                    <>
                      <Grid item xs={12} sm={6}>
                        <Controller
                          name="delay"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Delay"
                              fullWidth
                              placeholder="100ms"
                              helperText="e.g., 100ms, 1s"
                            />
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Controller
                          name="jitter"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Jitter (optional)"
                              fullWidth
                              placeholder="10ms"
                            />
                          )}
                        />
                      </Grid>
                    </>
                  )}

                  {watch('rule_type') === 'loss' && (
                    <Grid item xs={12} sm={6}>
                      <Controller
                        name="loss_percent"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Loss Percentage"
                            type="number"
                            fullWidth
                            inputProps={{ min: 0, max: 100, step: 0.1 }}
                          />
                        )}
                      />
                    </Grid>
                  )}

                  {watch('rule_type') === 'duplicate' && (
                    <Grid item xs={12} sm={6}>
                      <Controller
                        name="duplicate_percent"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Duplication Percentage"
                            type="number"
                            fullWidth
                            inputProps={{ min: 0, max: 100, step: 0.1 }}
                          />
                        )}
                      />
                    </Grid>
                  )}

                  {watch('rule_type') === 'corrupt' && (
                    <Grid item xs={12} sm={6}>
                      <Controller
                        name="corrupt_percent"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Corruption Percentage"
                            type="number"
                            fullWidth
                            inputProps={{ min: 0, max: 100, step: 0.1 }}
                          />
                        )}
                      />
                    </Grid>
                  )}

                  <Grid item xs={12} sm={6}>
                    <Controller
                      name="correlation"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Correlation (optional)"
                          type="number"
                          fullWidth
                          inputProps={{ min: 0, max: 100, step: 0.1 }}
                          helperText="Correlation percentage"
                        />
                      )}
                    />
                  </Grid>
                </>
              )}

              <Grid item xs={12}>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Description (optional)"
                      fullWidth
                      multiline
                      rows={2}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
            <Button 
              type="submit" 
              variant="contained"
              disabled={createBandwidthMutation.isLoading || createShapingMutation.isLoading}
            >
              {(createBandwidthMutation.isLoading || createShapingMutation.isLoading) ? 
                <CircularProgress size={20} /> : 'Create'
              }
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* View Rule Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Traffic Rule Details</DialogTitle>
        <DialogContent>
          {selectedRule && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedRule.name}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Container:</strong> {selectedRule.container_name || selectedRule.container_id}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Type:</strong> {selectedRule.rule_type.replace('_', ' ')}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Status:</strong> {selectedRule.status}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Interface:</strong> {selectedRule.interface || 'Auto-detected'}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Created:</strong> {new Date(selectedRule.created_at).toLocaleString()}
              </Typography>
              {selectedRule.applied_at && (
                <Typography variant="body2" paragraph>
                  <strong>Applied:</strong> {new Date(selectedRule.applied_at).toLocaleString()}
                </Typography>
              )}
              {selectedRule.description && (
                <Typography variant="body2" paragraph>
                  <strong>Description:</strong> {selectedRule.description}
                </Typography>
              )}

              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1">Parameters</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <pre style={{ fontSize: '0.875rem', background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                    {JSON.stringify(selectedRule.parameters, null, 2)}
                  </pre>
                </AccordionDetails>
              </Accordion>

              {selectedRule.tc_commands && selectedRule.tc_commands.length > 0 && (
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">
                      <CodeIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      TC Commands
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box>
                      {selectedRule.tc_commands.map((command, index) => (
                        <Typography 
                          key={index} 
                          variant="body2" 
                          component="div"
                          sx={{ 
                            fontFamily: 'monospace', 
                            background: '#f5f5f5', 
                            p: 1, 
                            mb: 1, 
                            borderRadius: 1 
                          }}
                        >
                          {command}
                        </Typography>
                      ))}
                    </Box>
                  </AccordionDetails>
                </Accordion>
              )}

              {selectedRule.error_message && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  <strong>Error:</strong> {selectedRule.error_message}
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default TrafficControl;

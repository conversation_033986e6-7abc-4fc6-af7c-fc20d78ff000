import axios from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Container API
export const containerAPI = {
  list: (all = true) => api.get(`/api/v1/containers?all=${all}`),
  get: (id) => api.get(`/api/v1/containers/${id}`),
  create: (data) => api.post('/api/v1/containers', data),
  start: (id) => api.post(`/api/v1/containers/${id}/start`),
  stop: (id, timeout = 10) => api.post(`/api/v1/containers/${id}/stop?timeout=${timeout}`),
  restart: (id, timeout = 10) => api.post(`/api/v1/containers/${id}/restart?timeout=${timeout}`),
  delete: (id, force = false, removeVolumes = false) => 
    api.delete(`/api/v1/containers/${id}?force=${force}&remove_volumes=${removeVolumes}`),
  stats: (id) => api.get(`/api/v1/containers/${id}/stats`),
  logs: (id, tail = 100) => api.get(`/api/v1/containers/${id}/logs?tail=${tail}`),
};

// Network API
export const networkAPI = {
  list: () => api.get('/api/v1/networks'),
  get: (id) => api.get(`/api/v1/networks/${id}`),
  create: (data) => api.post('/api/v1/networks', data),
  createMacvlan: (data) => api.post('/api/v1/networks/macvlan', data),
  delete: (id) => api.delete(`/api/v1/networks/${id}`),
  connect: (networkId, data) => api.post(`/api/v1/networks/${networkId}/connect`, data),
  disconnect: (networkId, containerId, force = false) => 
    api.post(`/api/v1/networks/${networkId}/disconnect?container_id=${containerId}&force=${force}`),
  getContainers: (id) => api.get(`/api/v1/networks/${id}/containers`),
  getAvailableIPs: (id, limit = 50) => api.get(`/api/v1/networks/${id}/available-ips?limit=${limit}`),
};

// Monitoring API
export const monitoringAPI = {
  getSystem: () => api.get('/api/v1/monitoring/system'),
  getAllContainers: () => api.get('/api/v1/monitoring/containers'),
  getContainer: (id) => api.get(`/api/v1/monitoring/containers/${id}`),
  getNetworks: () => api.get('/api/v1/monitoring/networks'),
  getInterface: (name) => api.get(`/api/v1/monitoring/networks/${name}`),
  getDashboard: () => api.get('/api/v1/monitoring/dashboard'),
  getContainerBandwidth: (id) => api.get(`/api/v1/monitoring/containers/${id}/bandwidth`),
  getContainerHistory: (id, hours = 24) => 
    api.get(`/api/v1/monitoring/containers/${id}/history?hours=${hours}`),
  getAlerts: () => api.get('/api/v1/monitoring/alerts'),
  setThresholds: (data) => api.post('/api/v1/monitoring/alerts/thresholds', data),
};

// Scheduling API
export const schedulingAPI = {
  list: (enabledOnly = false) => api.get(`/api/v1/scheduling?enabled_only=${enabledOnly}`),
  get: (id) => api.get(`/api/v1/scheduling/${id}`),
  createContainer: (data) => api.post('/api/v1/scheduling/container', data),
  createScript: (data) => api.post('/api/v1/scheduling/script', data),
  update: (id, data) => api.put(`/api/v1/scheduling/${id}`, data),
  delete: (id) => api.delete(`/api/v1/scheduling/${id}`),
  enable: (id) => api.post(`/api/v1/scheduling/${id}/enable`),
  disable: (id) => api.post(`/api/v1/scheduling/${id}/disable`),
  executeNow: (id) => api.post(`/api/v1/scheduling/${id}/execute`),
  getExecutions: (id, limit = 50) => api.get(`/api/v1/scheduling/${id}/executions?limit=${limit}`),
  getRecentExecutions: (limit = 100) => api.get(`/api/v1/scheduling/executions/recent?limit=${limit}`),
  validateCron: (expression) => api.get(`/api/v1/scheduling/validate/cron/${encodeURIComponent(expression)}`),
};

// Traffic Control API
export const trafficAPI = {
  listRules: (containerId = null) => {
    const params = containerId ? `?container_id=${containerId}` : '';
    return api.get(`/api/v1/traffic/rules${params}`);
  },
  getRule: (id) => api.get(`/api/v1/traffic/rules/${id}`),
  createBandwidthLimit: (data) => api.post('/api/v1/traffic/bandwidth-limit', data),
  createShapingRule: (data) => api.post('/api/v1/traffic/shaping-rule', data),
  deleteRule: (id) => api.delete(`/api/v1/traffic/rules/${id}`),
  applyRule: (id) => api.post(`/api/v1/traffic/rules/${id}/apply`),
  removeRule: (id) => api.post(`/api/v1/traffic/rules/${id}/remove`),
  getContainerInterface: (id) => api.get(`/api/v1/traffic/container/${id}/interface`),
  getContainerRules: (id) => api.get(`/api/v1/traffic/container/${id}/current-rules`),
  clearContainerRules: (id) => api.post(`/api/v1/traffic/container/${id}/clear-all`),
  previewCommands: (id) => api.get(`/api/v1/traffic/commands/preview/${id}`),
  getStatus: () => api.get('/api/v1/traffic/status'),
};

// WebSocket connection for real-time monitoring
export const createWebSocketConnection = (onMessage, onError = null) => {
  const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws/monitoring';
  const ws = new WebSocket(wsUrl);
  
  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      onMessage(data);
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  };
  
  ws.onerror = (error) => {
    console.error('WebSocket error:', error);
    if (onError) onError(error);
  };
  
  ws.onclose = () => {
    console.log('WebSocket connection closed');
  };
  
  return ws;
};

export default api;

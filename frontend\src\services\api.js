import axios from 'axios';

// Global variable to store current server URL
let currentServerUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Function to update the current server URL
export const setCurrentServerUrl = (url) => {
  currentServerUrl = url;
};

// Function to get the current server URL
export const getCurrentServerUrl = () => {
  return currentServerUrl;
};

// Create axios instance with dynamic baseURL
const createApiInstance = () => {
  return axios.create({
    baseURL: currentServerUrl,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// Get current API instance
const getApi = () => {
  const api = createApiInstance();

  // Request interceptor
  api.interceptors.request.use(
    (config) => {
      // Update baseURL to current server
      config.baseURL = currentServerUrl;

      // Add auth token if available
      const token = localStorage.getItem('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor with retry logic
  api.interceptors.response.use(
    (response) => {
      return response;
    },
    async (error) => {
      const originalRequest = error.config;

      // Handle network errors with retry
      if (error.code === 'ECONNABORTED' || error.code === 'NETWORK_ERROR') {
        if (!originalRequest._retry && originalRequest._retryCount < 2) {
          originalRequest._retry = true;
          originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * originalRequest._retryCount));

          return getApi().request(originalRequest);
        }
      }

      if (error.response?.status === 401) {
        // Handle unauthorized access
        localStorage.removeItem('authToken');
        window.location.href = '/login';
      }

      return Promise.reject(error);
    }
  );

  return api;
};

// Container API
export const containerAPI = {
  list: (all = true) => getApi().get(`/api/v1/containers?all=${all}`),
  get: (id) => getApi().get(`/api/v1/containers/${id}`),
  create: (data) => getApi().post('/api/v1/containers', data),
  start: (id) => getApi().post(`/api/v1/containers/${id}/start`),
  stop: (id, timeout = 10) => getApi().post(`/api/v1/containers/${id}/stop?timeout=${timeout}`),
  restart: (id, timeout = 10) => getApi().post(`/api/v1/containers/${id}/restart?timeout=${timeout}`),
  delete: (id, force = false, removeVolumes = false) =>
    getApi().delete(`/api/v1/containers/${id}?force=${force}&remove_volumes=${removeVolumes}`),
  stats: (id) => getApi().get(`/api/v1/containers/${id}/stats`),
  logs: (id, tail = 100) => getApi().get(`/api/v1/containers/${id}/logs?tail=${tail}`),
};

// Network API
export const networkAPI = {
  list: () => getApi().get('/api/v1/networks'),
  get: (id) => getApi().get(`/api/v1/networks/${id}`),
  create: (data) => getApi().post('/api/v1/networks', data),
  createMacvlan: (data) => getApi().post('/api/v1/networks/macvlan', data),
  delete: (id) => getApi().delete(`/api/v1/networks/${id}`),
  connect: (networkId, data) => getApi().post(`/api/v1/networks/${networkId}/connect`, data),
  disconnect: (networkId, containerId, force = false) =>
    getApi().post(`/api/v1/networks/${networkId}/disconnect?container_id=${containerId}&force=${force}`),
  getContainers: (id) => getApi().get(`/api/v1/networks/${id}/containers`),
  getAvailableIPs: (id, limit = 50) => getApi().get(`/api/v1/networks/${id}/available-ips?limit=${limit}`),
};

// Monitoring API
export const monitoringAPI = {
  getSystem: () => getApi().get('/api/v1/monitoring/system'),
  getAllContainers: () => getApi().get('/api/v1/monitoring/containers'),
  getContainer: (id) => getApi().get(`/api/v1/monitoring/containers/${id}`),
  getNetworks: () => getApi().get('/api/v1/monitoring/networks'),
  getInterface: (name) => getApi().get(`/api/v1/monitoring/networks/${name}`),
  getDashboard: () => getApi().get('/api/v1/monitoring/dashboard'),
  getContainerBandwidth: (id) => getApi().get(`/api/v1/monitoring/containers/${id}/bandwidth`),
  getContainerHistory: (id, hours = 24) =>
    getApi().get(`/api/v1/monitoring/containers/${id}/history?hours=${hours}`),
  getAlerts: () => getApi().get('/api/v1/monitoring/alerts'),
  setThresholds: (data) => getApi().post('/api/v1/monitoring/alerts/thresholds', data),
};

// Scheduling API
export const schedulingAPI = {
  list: (enabledOnly = false) => getApi().get(`/api/v1/scheduling?enabled_only=${enabledOnly}`),
  get: (id) => getApi().get(`/api/v1/scheduling/${id}`),
  createContainer: (data) => getApi().post('/api/v1/scheduling/container', data),
  createScript: (data) => getApi().post('/api/v1/scheduling/script', data),
  update: (id, data) => getApi().put(`/api/v1/scheduling/${id}`, data),
  delete: (id) => getApi().delete(`/api/v1/scheduling/${id}`),
  enable: (id) => getApi().post(`/api/v1/scheduling/${id}/enable`),
  disable: (id) => getApi().post(`/api/v1/scheduling/${id}/disable`),
  executeNow: (id) => getApi().post(`/api/v1/scheduling/${id}/execute`),
  getExecutions: (id, limit = 50) => getApi().get(`/api/v1/scheduling/${id}/executions?limit=${limit}`),
  getRecentExecutions: (limit = 100) => getApi().get(`/api/v1/scheduling/executions/recent?limit=${limit}`),
  validateCron: (expression) => getApi().get(`/api/v1/scheduling/validate/cron/${encodeURIComponent(expression)}`),
};

// Traffic Control API
export const trafficAPI = {
  listRules: (containerId = null) => {
    const params = containerId ? `?container_id=${containerId}` : '';
    return getApi().get(`/api/v1/traffic/rules${params}`);
  },
  getRule: (id) => getApi().get(`/api/v1/traffic/rules/${id}`),
  createBandwidthLimit: (data) => getApi().post('/api/v1/traffic/bandwidth-limit', data),
  createShapingRule: (data) => getApi().post('/api/v1/traffic/shaping-rule', data),
  deleteRule: (id) => getApi().delete(`/api/v1/traffic/rules/${id}`),
  applyRule: (id) => getApi().post(`/api/v1/traffic/rules/${id}/apply`),
  removeRule: (id) => getApi().post(`/api/v1/traffic/rules/${id}/remove`),
  getContainerInterface: (id) => getApi().get(`/api/v1/traffic/container/${id}/interface`),
  getContainerRules: (id) => getApi().get(`/api/v1/traffic/container/${id}/current-rules`),
  clearContainerRules: (id) => getApi().post(`/api/v1/traffic/container/${id}/clear-all`),
  previewCommands: (id) => getApi().get(`/api/v1/traffic/commands/preview/${id}`),
  getStatus: () => getApi().get('/api/v1/traffic/status'),
};

// WebSocket connection for real-time monitoring
export const createWebSocketConnection = (onMessage, onError = null) => {
  // Use current server URL for WebSocket connection
  const serverUrl = getCurrentServerUrl();
  const wsUrl = serverUrl.replace('http://', 'ws://').replace('https://', 'wss://') + '/ws/monitoring';

  const ws = new WebSocket(wsUrl);

  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      onMessage(data);
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  };

  ws.onerror = (error) => {
    console.error('WebSocket error:', error);
    if (onError) onError(error);
  };

  ws.onclose = () => {
    console.log('WebSocket connection closed');
  };

  return ws;
};

// Server health check API
export const serverAPI = {
  health: (serverUrl) => {
    const api = axios.create({
      baseURL: serverUrl,
      timeout: 5000,
    });
    return api.get('/health');
  },
  info: (serverUrl) => {
    const api = axios.create({
      baseURL: serverUrl,
      timeout: 5000,
    });
    return api.get('/');
  },
};

export default getApi;

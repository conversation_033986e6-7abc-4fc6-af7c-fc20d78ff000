#!/bin/bash

# Docker Management Tool - Startup Script
#
# Usage:
#   ./start.sh [mode]
#
# Modes:
#   single       - Single instance (default) - Frontend + Backend + Redis
#   distributed  - Multiple backend instances for testing distributed architecture
#   backend-only - Backend only (for remote server deployment)
#
# Examples:
#   ./start.sh                    # Start single instance
#   ./start.sh distributed        # Start distributed architecture
#   ./start.sh backend-only       # Start backend only

set -e

# Parse command line arguments
DEPLOYMENT_MODE=${1:-"single"}

# Validate deployment mode
case $DEPLOYMENT_MODE in
    "single"|"distributed"|"backend-only")
        ;;
    *)
        echo "❌ Invalid deployment mode: $DEPLOYMENT_MODE"
        echo "Valid modes: single, distributed, backend-only"
        exit 1
        ;;
esac

echo "🐳 Starting Docker Management Tool..."
echo "Deployment Mode: $DEPLOYMENT_MODE"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create environment file if it doesn't exist
if [ ! -f backend/.env ]; then
    echo "📝 Creating environment file..."
    cp backend/.env.example backend/.env
    echo "✅ Environment file created. Please review backend/.env and update as needed."
fi

# Choose deployment method based on mode
case $DEPLOYMENT_MODE in
    "distributed")
        echo "🔨 Building and starting distributed services..."
        docker-compose -f docker-compose.distributed.yml up --build -d
        COMPOSE_FILE="docker-compose.distributed.yml"
        ;;
    "backend-only")
        echo "🔨 Building and starting backend-only services..."
        docker-compose -f docker-compose.backend-only.yml up --build -d
        COMPOSE_FILE="docker-compose.backend-only.yml"
        ;;
    *)
        echo "🔨 Building and starting single-instance services..."
        docker-compose up --build -d
        COMPOSE_FILE="docker-compose.yml"
        ;;
esac

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check service status
echo "🔍 Checking service status..."

# Check backend
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Backend API is running at http://localhost:8000"
    echo "📚 API Documentation: http://localhost:8000/docs"
else
    echo "⚠️  Backend API is not responding yet. It may still be starting up."
fi

# Check frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is running at http://localhost:3000"
else
    echo "⚠️  Frontend is not responding yet. It may still be starting up."
fi

# Check services based on deployment mode
if [ "$DEPLOYMENT_MODE" = "distributed" ]; then
    # Check multiple Redis instances
    for i in {1..3}; do
        if docker-compose -f $COMPOSE_FILE exec -T redis-server-$i redis-cli ping > /dev/null 2>&1; then
            echo "✅ Redis Server $i is running"
        else
            echo "⚠️  Redis Server $i is not responding"
        fi
    done

    echo ""
    echo "🎉 Distributed Docker Management Tool is starting up!"
    echo ""
    echo "📱 Web Interface: http://localhost:3000"
    echo "🔧 Backend APIs:"
    echo "   Server 1: http://localhost:8000 (docs: http://localhost:8000/docs)"
    echo "   Server 2: http://localhost:8001 (docs: http://localhost:8001/docs)"
    echo "   Server 3: http://localhost:8002 (docs: http://localhost:8002/docs)"

elif [ "$DEPLOYMENT_MODE" = "backend-only" ]; then
    # Check single Redis instance
    if docker-compose -f $COMPOSE_FILE exec -T redis redis-cli ping > /dev/null 2>&1; then
        echo "✅ Redis is running"
    else
        echo "⚠️  Redis is not responding"
    fi

    echo ""
    echo "🎉 Backend-only Docker Management Tool is starting up!"
    echo ""
    echo "🔧 API Endpoint: http://localhost:8000"
    echo "📖 API Docs: http://localhost:8000/docs"

else
    # Check single Redis instance
    if docker-compose -f $COMPOSE_FILE exec -T redis redis-cli ping > /dev/null 2>&1; then
        echo "✅ Redis is running"
    else
        echo "⚠️  Redis is not responding"
    fi

    echo ""
    echo "🎉 Docker Management Tool is starting up!"
    echo ""
    echo "📱 Web Interface: http://localhost:3000"
    echo "🔧 API Endpoint: http://localhost:8000"
    echo "📖 API Docs: http://localhost:8000/docs"
fi

echo ""
echo "📋 To view logs:"
echo "   docker-compose -f $COMPOSE_FILE logs -f"
echo ""
echo "🛑 To stop:"
echo "   docker-compose -f $COMPOSE_FILE down"
echo ""
echo "🔄 To restart:"
echo "   docker-compose -f $COMPOSE_FILE restart"
echo ""

# Show usage information for distributed mode
if [ "$DEPLOYMENT_MODE" = "distributed" ]; then
    echo "🔧 Server Management:"
    echo "   1. Open the web interface at http://localhost:3000"
    echo "   2. Go to the 'Servers' page to manage backend servers"
    echo "   3. Use the server selector in the header to switch between servers"
    echo ""
fi

# Show container status
echo "📊 Container Status:"
docker-compose -f $COMPOSE_FILE ps
